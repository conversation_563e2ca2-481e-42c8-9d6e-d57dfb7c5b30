"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Upload, Save, User, Users, GraduationCap, Heart, FileText } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export interface StudentFormData {
  // Basic Info
  first_name: string
  last_name: string
  father_name: string
  date_of_birth: string
  gender: string
  nationality: string
  religion: string
  marital_status: string
  photo: File | null
  photoPreview: string

  // Pakistani Identity Documents
  cnic_number: string
  b_form_number: string

  // Contact Information
  phone_number: string
  email: string

  // Address (Pakistani format)
  address_line_1: string
  address_line_2: string
  address: string // Legacy field for backward compatibility
  city: string
  province: string
  postal_code: string

  // Personal Details
  blood_group: string

  // Medical Information
  medical_conditions: string
  allergies: string
  emergency_medical_contact: string

  // Academic Information
  previous_education: string
  matric_marks: string
  intermediate_marks: string

  // Guardian Info
  guardian_name: string
  guardian_relationship: string
  guardian_phone: string
  guardian_email: string
  guardian_occupation: string
  guardian_address: string
  guardian_cnic: string

  // Academic Info
  academic_year: string
  term: string
  grade: string
  section: string
  admission_date: string
  class_level: string
  previous_school: string

  // Medical Info (additional)
  emergency_contact: string
  emergency_phone: string
  height: string
  weight: string

  // Additional fields
  is_active: boolean

  // Document files
  birth_certificate: File | null
  academic_transcripts: File | null
  medical_records: File | null
  financial_documents: File | null

  // Document URLs (for existing documents in edit mode)
  birth_certificate_url: string
  academic_transcripts_url: string
  medical_records_url: string
  financial_documents_url: string
}

interface StudentFormProps {
  initialData?: Partial<StudentFormData>
  isEditing?: boolean
  onSubmit: (formData: StudentFormData) => Promise<void>
  isLoading?: boolean
}

export default function StudentForm({ initialData, isEditing = false, onSubmit, isLoading = false }: StudentFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const { toast } = useToast()

  // Form state
  const [formData, setFormData] = useState<StudentFormData>({
    // Basic Info
    first_name: "",
    last_name: "",
    father_name: "",
    date_of_birth: "",
    gender: "",
    nationality: "",
    religion: "",
    marital_status: "",
    photo: null,
    photoPreview: "",

    // Pakistani Identity Documents
    cnic_number: "",
    b_form_number: "",

    // Contact Information
    phone_number: "",
    email: "",

    // Address (Pakistani format)
    address_line_1: "",
    address_line_2: "",
    address: "", // Legacy field for backward compatibility
    city: "",
    province: "",
    postal_code: "",

    // Personal Details
    blood_group: "",

    // Medical Information
    medical_conditions: "",
    allergies: "",
    emergency_medical_contact: "",

    // Academic Information
    previous_education: "",
    matric_marks: "",
    intermediate_marks: "",

    // Guardian Info
    guardian_name: "",
    guardian_relationship: "",
    guardian_phone: "",
    guardian_email: "",
    guardian_occupation: "",
    guardian_address: "",
    guardian_cnic: "",

    // Academic Info
    academic_year: "",
    term: "",
    grade: "",
    section: "",
    admission_date: "",
    class_level: "",
    previous_school: "",

    // Medical Info (additional)
    emergency_contact: "",
    emergency_phone: "",
    height: "",
    weight: "",

    // Additional fields
    is_active: true,

    // Document files
    birth_certificate: null,
    academic_transcripts: null,
    medical_records: null,
    financial_documents: null,

    // Document URLs (for existing documents in edit mode)
    birth_certificate_url: "",
    academic_transcripts_url: "",
    medical_records_url: "",
    financial_documents_url: ""
  })

  // Field errors state
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})

  // Initialize form with provided data
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        // Handle null values for file fields
        photo: initialData.photo || null,
        birth_certificate: initialData.birth_certificate || null,
        academic_transcripts: initialData.academic_transcripts || null,
        medical_records: initialData.medical_records || null,
        financial_documents: initialData.financial_documents || null,
      }))
    }
  }, [initialData])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
    
    // Real-time validation for specific fields
    validateField(field, value)
  }

  const generateStudentIdPreview = () => {
    const currentYear = new Date().getFullYear()
    return `GIHD${currentYear}XXXX (Auto-generated)`
  }

  const validateField = (field: string, value: string) => {
    let error = ''
    
    switch (field) {
      case 'cnic_number':
        if (value && !value.match(/^\d{5}-\d{7}-\d{1}$/)) {
          error = 'CNIC must be in format: 12345-1234567-1'
        }
        break
      case 'phone_number':
      case 'guardian_phone':
        if (value && !value.match(/^(\+92\d{10}|0\d{10})$/)) {
          error = 'Phone must be in format: +923001234567 or 03001234567'
        }
        break
      case 'guardian_cnic':
        if (value && !value.match(/^\d{5}-\d{7}-\d{1}$/)) {
          error = 'Guardian CNIC must be in format: 12345-1234567-1'
        }
        break
      case 'email':
      case 'guardian_email':
        if (value && !value.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
          error = 'Please enter a valid email address'
        }
        break
      case 'first_name':
      case 'last_name':
        if (!value.trim()) {
          error = 'This field is required'
        }
        break
      case 'date_of_birth':
        if (!value) {
          error = 'Date of birth is required'
        } else {
          const birthDate = new Date(value)
          const today = new Date()
          const age = today.getFullYear() - birthDate.getFullYear()
          if (age > 25 || age < 3) {
            error = 'Please enter a valid birth date for a student'
          }
        }
        break
    }
    
    if (error) {
      setFieldErrors(prev => ({ ...prev, [field]: error }))
    }
  }

  const formatCNIC = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '')
    
    // Apply CNIC format: 12345-1234567-1
    if (digits.length <= 5) {
      return digits
    } else if (digits.length <= 12) {
      return `${digits.slice(0, 5)}-${digits.slice(5)}`
    } else {
      return `${digits.slice(0, 5)}-${digits.slice(5, 12)}-${digits.slice(12, 13)}`
    }
  }

  const formatPhone = (value: string) => {
    // Remove all non-digits and plus
    let cleaned = value.replace(/[^+\d]/g, '')
    
    // If starts with 0, convert to +92
    if (cleaned.startsWith('0')) {
      cleaned = '+92' + cleaned.slice(1)
    }
    
    // If doesn't start with +92, add it for Pakistani numbers
    if (!cleaned.startsWith('+') && cleaned.length > 0) {
      cleaned = '+92' + cleaned
    }
    
    return cleaned
  }

  // Cleanup photo preview URL on component unmount
  useEffect(() => {
    return () => {
      if (formData.photoPreview) {
        URL.revokeObjectURL(formData.photoPreview)
      }
    }
  }, [formData.photoPreview])

  const handleDocumentUpload = (documentType: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid File Type",
          description: "Please select a PDF, JPG, or PNG file",
          variant: "destructive",
        })
        return
      }
      
      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select a file smaller than 5MB",
          variant: "destructive",
        })
        return
      }
      
      setFormData(prev => ({
        ...prev,
        [documentType]: file
      }))
      
      const documentNames: Record<string, string> = {
        birth_certificate: "Birth Certificate",
        academic_transcripts: "Academic Transcripts",
        medical_records: "Medical Records",
        financial_documents: "Financial Documents"
      }
      
      toast({
        title: "Document Uploaded",
        description: `${documentNames[documentType]}: ${file.name}`,
      })
    }
  }

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid File Type",
          description: "Please select a valid image file (JPG, PNG, GIF)",
          variant: "destructive",
        })
        return
      }
      
      // Validate file size (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please select an image smaller than 2MB",
          variant: "destructive",
        })
        return
      }
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      
      setFormData(prev => ({
        ...prev,
        photo: file,
        photoPreview: previewUrl
      }))
      
      toast({
        title: "Photo Uploaded",
        description: `Selected: ${file.name}`,
      })
    }
  }

  const validateForm = () => {
    const requiredFields = {
      first_name: 'First Name',
      last_name: 'Last Name', 
      date_of_birth: 'Date of Birth',
      gender: 'Gender',
      city: 'City',
      province: 'Province'
    }
    
    const newFieldErrors: Record<string, string> = {}
    let hasErrors = false

    // Check required fields
    Object.entries(requiredFields).forEach(([field, label]) => {
      const value = formData[field as keyof StudentFormData]
      if (!value || (typeof value === 'string' && !value.trim())) {
        newFieldErrors[field] = `${label} is required`
        hasErrors = true
      }
    })
    
    // Check existing field errors
    if (Object.keys(fieldErrors).length > 0) {
      hasErrors = true
    }
    
    if (hasErrors) {
      const allErrors = { ...fieldErrors, ...newFieldErrors }
      setFieldErrors(allErrors)
      
      // Show specific errors in toast
      const errorMessages = Object.entries(allErrors).map(([field, error]) => {
        const fieldLabels: Record<string, string> = {
          first_name: 'First Name',
          last_name: 'Last Name',
          date_of_birth: 'Date of Birth',
          gender: 'Gender',
          city: 'City',
          province: 'Province',
          cnic_number: 'CNIC',
          guardian_cnic: 'Guardian CNIC',
          guardian_phone: 'Guardian Phone',
          guardian_email: 'Guardian Email'
        }
        return `${fieldLabels[field] || field}: ${error}`
      })
      
      const totalErrors = Object.keys(allErrors).length
      toast({
        title: "Validation Error", 
        description: totalErrors === 1
          ? errorMessages[0]
          : `${totalErrors} errors found:\n${errorMessages.slice(0, 3).join('\n')}${totalErrors > 3 ? `\n...and ${totalErrors - 3} more` : ''}`,
        variant: "destructive",
      })
      return false
    }
    
    return true
  }

  const handleSubmit = async () => {
    if (!validateForm()) return
    await onSubmit(formData)
  }

  return (
    <div className="max-w-6xl mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <TabsList className="grid w-full grid-cols-5 bg-indigo-50">
            <TabsTrigger value="basic" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              <User className="h-4 w-4 mr-2" />
              Basic Info
            </TabsTrigger>
            <TabsTrigger
              value="guardian"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              <Users className="h-4 w-4 mr-2" />
              Guardian
            </TabsTrigger>
            <TabsTrigger
              value="academic"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              <GraduationCap className="h-4 w-4 mr-2" />
              Academic
            </TabsTrigger>
            <TabsTrigger value="medical" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              <Heart className="h-4 w-4 mr-2" />
              Medical
            </TabsTrigger>
            <TabsTrigger
              value="documents"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              <FileText className="h-4 w-4 mr-2" />
              Documents
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="basic" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900 flex items-center">
                <User className="h-5 w-5 mr-2" />
                Basic Information
              </CardTitle>
              <CardDescription>Student personal details and identification information</CardDescription>
            </CardHeader>
            <CardContent className="p-8 space-y-8">
              {/* Photo Upload Section */}
              <div className="flex items-center space-x-8">
                <Avatar className="h-32 w-32 border-4 border-indigo-100">
                  <AvatarImage src={formData.photoPreview || "/placeholder.svg?height=128&width=128"} />
                  <AvatarFallback className="bg-indigo-100 text-indigo-600 text-2xl">
                    {formData.first_name ? formData.first_name.charAt(0).toUpperCase() : "ST"}
                    {formData.last_name ? formData.last_name.charAt(0).toUpperCase() : ""}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Student Photo</h3>
                    <p className="text-sm text-gray-600">Upload a clear photo of the student for identification</p>
                  </div>
                  <div className="relative">
                    <input
                      type="file"
                      id="photo-upload"
                      accept="image/*"
                      onChange={handlePhotoUpload}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <Button
                      variant="outline"
                      className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                      type="button"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {formData.photo ? 'Change Photo' : 'Upload Photo'}
                    </Button>
                  </div>
                  {formData.photo && (
                    <p className="text-sm text-green-600 mt-1">
                      ✓ Selected: {formData.photo.name}
                    </p>
                  )}
                  <p className="text-xs text-gray-500">JPG, PNG up to 2MB. Recommended size: 400x400px</p>
                </div>
              </div>

              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-sm font-semibold text-gray-700">
                    First Name *
                  </Label>
                  <Input
                    id="firstName"
                    placeholder="Enter first name"
                    className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                      fieldErrors.first_name ? 'border-red-500 focus:border-red-500' : ''
                    }`}
                    value={formData.first_name}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                  />
                  {fieldErrors.first_name && (
                    <p className="text-sm text-red-600">{fieldErrors.first_name}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-sm font-semibold text-gray-700">
                    Last Name *
                  </Label>
                  <Input
                    id="lastName"
                    placeholder="Enter last name"
                    className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                      fieldErrors.last_name ? 'border-red-500 focus:border-red-500' : ''
                    }`}
                    value={formData.last_name}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                  />
                  {fieldErrors.last_name && (
                    <p className="text-sm text-red-600">{fieldErrors.last_name}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fatherName" className="text-sm font-semibold text-gray-700">
                    Father's Name
                  </Label>
                  <Input
                    id="fatherName"
                    placeholder="Enter father's name"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.father_name}
                    onChange={(e) => handleInputChange('father_name', e.target.value)}
                  />
                </div>
                {!isEditing && (
                  <div className="space-y-2">
                    <Label htmlFor="studentId" className="text-sm font-semibold text-gray-700">
                      Student ID
                    </Label>
                    <Input
                      id="studentId"
                      placeholder="GIHD-2024-XXX (Auto-generated)"
                      disabled
                      className="bg-gray-50"
                      value={generateStudentIdPreview()}
                    />
                  </div>
                )}
                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth" className="text-sm font-semibold text-gray-700">
                    Date of Birth *
                  </Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                      fieldErrors.date_of_birth ? 'border-red-500 focus:border-red-500' : ''
                    }`}
                    value={formData.date_of_birth}
                    onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                  />
                  {fieldErrors.date_of_birth && (
                    <p className="text-sm text-red-600">{fieldErrors.date_of_birth}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="gender" className="text-sm font-semibold text-gray-700">
                    Gender *
                  </Label>
                  <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nationality" className="text-sm font-semibold text-gray-700">
                    Nationality
                  </Label>
                  <Input
                    id="nationality"
                    placeholder="Enter nationality"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.nationality}
                    onChange={(e) => handleInputChange('nationality', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cnic" className="text-sm font-semibold text-gray-700">
                    CNIC Number (18+ years)
                  </Label>
                  <div className="space-y-2">
                    <Input
                      id="cnic"
                      placeholder="12345-1234567-1"
                      className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                        fieldErrors.cnic_number ? 'border-red-500 focus:border-red-500' : ''
                      }`}
                      value={formData.cnic_number}
                      onChange={(e) => {
                        const formatted = formatCNIC(e.target.value)
                        handleInputChange('cnic_number', formatted)
                      }}
                      maxLength={15}
                    />
                    {fieldErrors.cnic_number && (
                      <p className="text-sm text-red-600">{fieldErrors.cnic_number}</p>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bform" className="text-sm font-semibold text-gray-700">
                    B-Form Number (Under 18)
                  </Label>
                  <Input
                    id="bform"
                    placeholder="Enter B-Form number for minors"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.b_form_number}
                    onChange={(e) => handleInputChange('b_form_number', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="religion" className="text-sm font-semibold text-gray-700">
                    Religion
                  </Label>
                  <Select value={formData.religion} onValueChange={(value) => handleInputChange('religion', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select religion" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Islam">Islam</SelectItem>
                      <SelectItem value="Christianity">Christianity</SelectItem>
                      <SelectItem value="Hinduism">Hinduism</SelectItem>
                      <SelectItem value="Buddhism">Buddhism</SelectItem>
                      <SelectItem value="Judaism">Judaism</SelectItem>
                      <SelectItem value="Sikhism">Sikhism</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maritalStatus" className="text-sm font-semibold text-gray-700">
                    Marital Status
                  </Label>
                  <Select value={formData.marital_status} onValueChange={(value) => handleInputChange('marital_status', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select marital status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="S">Single</SelectItem>
                      <SelectItem value="M">Married</SelectItem>
                      <SelectItem value="D">Divorced</SelectItem>
                      <SelectItem value="W">Widowed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber" className="text-sm font-semibold text-gray-700">
                    Phone Number
                  </Label>
                  <Input
                    id="phoneNumber"
                    placeholder="+92-300-1234567"
                    className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                      fieldErrors.phone_number ? 'border-red-500 focus:border-red-500' : ''
                    }`}
                    value={formData.phone_number}
                    onChange={(e) => {
                      const formatted = formatPhone(e.target.value)
                      handleInputChange('phone_number', formatted)
                    }}
                  />
                  {fieldErrors.phone_number && (
                    <p className="text-sm text-red-600">{fieldErrors.phone_number}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="studentEmail" className="text-sm font-semibold text-gray-700">
                    Email Address
                  </Label>
                  <Input
                    id="studentEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                      fieldErrors.email ? 'border-red-500 focus:border-red-500' : ''
                    }`}
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                  {fieldErrors.email && (
                    <p className="text-sm text-red-600">{fieldErrors.email}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="addressLine1" className="text-sm font-semibold text-gray-700">
                  Address Line 1
                </Label>
                <Textarea
                  id="addressLine1"
                  placeholder="Enter street address"
                  value={formData.address_line_1 || formData.address}
                  onChange={(e) => {
                    handleInputChange('address_line_1', e.target.value)
                    handleInputChange('address', e.target.value) // Keep legacy field in sync
                  }}
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[80px]"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="addressLine2" className="text-sm font-semibold text-gray-700">
                  Address Line 2 (Optional)
                </Label>
                <Textarea
                  id="addressLine2"
                  placeholder="Enter additional address details"
                  value={formData.address_line_2}
                  onChange={(e) => handleInputChange('address_line_2', e.target.value)}
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[60px]"
                />
              </div>
              
              {/* City, Province, and Postal Code */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="city" className="text-sm font-semibold text-gray-700">
                    City *
                  </Label>
                  <Input
                    id="city"
                    placeholder="Enter city"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="province" className="text-sm font-semibold text-gray-700">
                    Province *
                  </Label>
                  <Select value={formData.province} onValueChange={(value) => handleInputChange('province', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select province" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Punjab">Punjab</SelectItem>
                      <SelectItem value="Sindh">Sindh</SelectItem>
                      <SelectItem value="KPK">Khyber Pakhtunkhwa</SelectItem>
                      <SelectItem value="Balochistan">Balochistan</SelectItem>
                      <SelectItem value="Gilgit-Baltistan">Gilgit-Baltistan</SelectItem>
                      <SelectItem value="AJK">Azad Jammu & Kashmir</SelectItem>
                      <SelectItem value="Islamabad">Islamabad Capital Territory</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="postalCode" className="text-sm font-semibold text-gray-700">
                    Postal Code
                  </Label>
                  <Input
                    id="postalCode"
                    placeholder="Enter postal code"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.postal_code}
                    onChange={(e) => handleInputChange('postal_code', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="guardian" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900 flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Guardian Information
              </CardTitle>
              <CardDescription>Primary and secondary guardian contact details</CardDescription>
            </CardHeader>
            <CardContent className="p-8 space-y-8">
              {/* Primary Guardian */}
              <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-900 mb-6">Primary Guardian</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="primaryName" className="text-sm font-semibold text-gray-700">
                      Full Name *
                    </Label>
                    <Input
                      id="primaryName"
                      placeholder="Enter guardian full name"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      value={formData.guardian_name}
                      onChange={(e) => handleInputChange('guardian_name', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="primaryRelation" className="text-sm font-semibold text-gray-700">
                      Relationship *
                    </Label>
                    <Select value={formData.guardian_relationship} onValueChange={(value) => handleInputChange('guardian_relationship', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select relationship" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="father">Father</SelectItem>
                        <SelectItem value="mother">Mother</SelectItem>
                        <SelectItem value="guardian">Legal Guardian</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="primaryPhone" className="text-sm font-semibold text-gray-700">
                      Phone Number *
                    </Label>
                    <Input
                      id="primaryPhone"
                      placeholder="+92-300-1234567"
                      className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                        fieldErrors.guardian_phone ? 'border-red-500 focus:border-red-500' : ''
                      }`}
                      value={formData.guardian_phone}
                      onChange={(e) => {
                        const formatted = formatPhone(e.target.value)
                        handleInputChange('guardian_phone', formatted)
                      }}
                    />
                    {fieldErrors.guardian_phone && (
                      <p className="text-sm text-red-600">{fieldErrors.guardian_phone}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="primaryEmail" className="text-sm font-semibold text-gray-700">
                      Email Address
                    </Label>
                    <Input
                      id="primaryEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                        fieldErrors.guardian_email ? 'border-red-500 focus:border-red-500' : ''
                      }`}
                      value={formData.guardian_email}
                      onChange={(e) => handleInputChange('guardian_email', e.target.value)}
                    />
                    {fieldErrors.guardian_email && (
                      <p className="text-sm text-red-600">{fieldErrors.guardian_email}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="primaryOccupation" className="text-sm font-semibold text-gray-700">
                      Occupation
                    </Label>
                    <Input
                      id="primaryOccupation"
                      placeholder="Enter occupation"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      value={formData.guardian_occupation}
                      onChange={(e) => handleInputChange('guardian_occupation', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="primaryCnic" className="text-sm font-semibold text-gray-700">
                      CNIC Number
                    </Label>
                    <Input
                      id="primaryCnic"
                      placeholder="12345-1234567-1"
                      className={`border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 ${
                        fieldErrors.guardian_cnic ? 'border-red-500 focus:border-red-500' : ''
                      }`}
                      value={formData.guardian_cnic}
                      onChange={(e) => {
                        const formatted = formatCNIC(e.target.value)
                        handleInputChange('guardian_cnic', formatted)
                      }}
                      maxLength={15}
                    />
                    {fieldErrors.guardian_cnic && (
                      <p className="text-sm text-red-600">{fieldErrors.guardian_cnic}</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="academic" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900 flex items-center">
                <GraduationCap className="h-5 w-5 mr-2" />
                Academic Information
              </CardTitle>
              <CardDescription>Class assignment and academic enrollment details</CardDescription>
            </CardHeader>
            <CardContent className="p-8 space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="academicYear" className="text-sm font-semibold text-gray-700">
                    Academic Year *
                  </Label>
                  <Select value={formData.academic_year} onValueChange={(value) => handleInputChange('academic_year', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select academic year" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2024-2025">2024-2025</SelectItem>
                      <SelectItem value="2023-2024">2023-2024</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="term" className="text-sm font-semibold text-gray-700">
                    Term/Semester *
                  </Label>
                  <Select value={formData.term} onValueChange={(value) => handleInputChange('term', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select term" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fall-2024">Fall 2024</SelectItem>
                      <SelectItem value="spring-2024">Spring 2024</SelectItem>
                      <SelectItem value="summer-2024">Summer 2024</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="grade" className="text-sm font-semibold text-gray-700">
                    Grade/Class *
                  </Label>
                  <Select value={formData.grade} onValueChange={(value) => handleInputChange('grade', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select grade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="grade-9">Grade 9</SelectItem>
                      <SelectItem value="grade-10">Grade 10</SelectItem>
                      <SelectItem value="grade-11">Grade 11</SelectItem>
                      <SelectItem value="grade-12">Grade 12</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="section" className="text-sm font-semibold text-gray-700">
                    Section *
                  </Label>
                  <Select value={formData.section} onValueChange={(value) => handleInputChange('section', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select section" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="a">Section A</SelectItem>
                      <SelectItem value="b">Section B</SelectItem>
                      <SelectItem value="c">Section C</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="classLevel" className="text-sm font-semibold text-gray-700">
                    Class Level
                  </Label>
                  <Input
                    id="classLevel"
                    placeholder="Enter class level"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.class_level}
                    onChange={(e) => handleInputChange('class_level', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="admissionDate" className="text-sm font-semibold text-gray-700">
                    Admission Date *
                  </Label>
                  <Input
                    id="admissionDate"
                    type="date"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.admission_date}
                    onChange={(e) => handleInputChange('admission_date', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="previousSchool" className="text-sm font-semibold text-gray-700">
                  Previous School (if any)
                </Label>
                <Input
                  id="previousSchool"
                  placeholder="Enter previous school name and details"
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  value={formData.previous_school}
                  onChange={(e) => handleInputChange('previous_school', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="previousEducation" className="text-sm font-semibold text-gray-700">
                  Previous Education Details
                </Label>
                <Textarea
                  id="previousEducation"
                  placeholder="Enter details about previous education, qualifications, etc."
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  value={formData.previous_education}
                  onChange={(e) => handleInputChange('previous_education', e.target.value)}
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="matricMarks" className="text-sm font-semibold text-gray-700">
                    Matric Marks (%)
                  </Label>
                  <Input
                    id="matricMarks"
                    type="number"
                    placeholder="Enter matric percentage"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.matric_marks}
                    onChange={(e) => handleInputChange('matric_marks', e.target.value)}
                    min="0"
                    max="100"
                    step="0.01"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="intermediateMarks" className="text-sm font-semibold text-gray-700">
                    Intermediate Marks (%)
                  </Label>
                  <Input
                    id="intermediateMarks"
                    type="number"
                    placeholder="Enter intermediate percentage"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.intermediate_marks}
                    onChange={(e) => handleInputChange('intermediate_marks', e.target.value)}
                    min="0"
                    max="100"
                    step="0.01"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medical" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900 flex items-center">
                <Heart className="h-5 w-5 mr-2" />
                Medical Information
              </CardTitle>
              <CardDescription>Health records and medical details for student safety</CardDescription>
            </CardHeader>
            <CardContent className="p-8 space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="bloodGroup" className="text-sm font-semibold text-gray-700">
                    Blood Group
                  </Label>
                  <Select value={formData.blood_group} onValueChange={(value) => handleInputChange('blood_group', value)}>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select blood group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="a+">A+</SelectItem>
                      <SelectItem value="a-">A-</SelectItem>
                      <SelectItem value="b+">B+</SelectItem>
                      <SelectItem value="b-">B-</SelectItem>
                      <SelectItem value="ab+">AB+</SelectItem>
                      <SelectItem value="ab-">AB-</SelectItem>
                      <SelectItem value="o+">O+</SelectItem>
                      <SelectItem value="o-">O-</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="height" className="text-sm font-semibold text-gray-700">
                    Height (cm)
                  </Label>
                  <Input
                    id="height"
                    type="number"
                    placeholder="Enter height in centimeters"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.height}
                    onChange={(e) => handleInputChange('height', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weight" className="text-sm font-semibold text-gray-700">
                    Weight (kg)
                  </Label>
                  <Input
                    id="weight"
                    type="number"
                    placeholder="Enter weight in kilograms"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.weight}
                    onChange={(e) => handleInputChange('weight', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="emergencyContact" className="text-sm font-semibold text-gray-700">
                    Emergency Contact
                  </Label>
                  <Input
                    id="emergencyContact"
                    placeholder="Emergency contact number"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    value={formData.emergency_contact}
                    onChange={(e) => handleInputChange('emergency_contact', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="allergies" className="text-sm font-semibold text-gray-700">
                    Known Allergies
                  </Label>
                  <Textarea
                    id="allergies"
                    placeholder="List any known allergies (food, medication, environmental, etc.)"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[80px]"
                    value={formData.allergies}
                    onChange={(e) => handleInputChange('allergies', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="medicalConditions" className="text-sm font-semibold text-gray-700">
                    Medical Conditions
                  </Label>
                  <Textarea
                    id="medicalConditions"
                    placeholder="List any chronic conditions, disabilities, or special medical needs"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[80px]"
                    value={formData.medical_conditions}
                    onChange={(e) => handleInputChange('medical_conditions', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900 flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Document Management
              </CardTitle>
              <CardDescription>Upload and manage required student documents</CardDescription>
            </CardHeader>
            <CardContent className="p-8 space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    Identity Documents
                  </h3>
                  <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                    <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                    <div className="mt-4">
                      <div className="relative">
                        <input
                          type="file"
                          id="birth-certificate-upload"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={(e) => handleDocumentUpload('birth_certificate', e)}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                          type="button"
                        >
                          {formData.birth_certificate ? 'Change Birth Certificate' : 'Upload Birth Certificate'}
                        </Button>
                      </div>
                      {formData.birth_certificate && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ Selected: {formData.birth_certificate.name}
                        </p>
                      )}
                      {isEditing && formData.birth_certificate_url && !formData.birth_certificate && (
                        <p className="text-sm text-blue-600 mt-1">
                          📄 Current: Birth Certificate on file
                        </p>
                      )}
                      <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    Academic Documents
                  </h3>
                  <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                    <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                    <div className="mt-4">
                      <div className="relative">
                        <input
                          type="file"
                          id="academic-transcripts-upload"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={(e) => handleDocumentUpload('academic_transcripts', e)}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                          type="button"
                        >
                          {formData.academic_transcripts ? 'Change Academic Transcripts' : 'Upload Previous Transcripts'}
                        </Button>
                      </div>
                      {formData.academic_transcripts && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ Selected: {formData.academic_transcripts.name}
                        </p>
                      )}
                      {isEditing && formData.academic_transcripts_url && !formData.academic_transcripts && (
                        <p className="text-sm text-blue-600 mt-1">
                          📄 Current: Academic Transcripts on file
                        </p>
                      )}
                      <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Medical Documents
                  </h3>
                  <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                    <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                    <div className="mt-4">
                      <div className="relative">
                        <input
                          type="file"
                          id="medical-records-upload"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={(e) => handleDocumentUpload('medical_records', e)}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                          type="button"
                        >
                          {formData.medical_records ? 'Change Medical Records' : 'Upload Medical Records'}
                        </Button>
                      </div>
                      {formData.medical_records && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ Selected: {formData.medical_records.name}
                        </p>
                      )}
                      {isEditing && formData.medical_records_url && !formData.medical_records && (
                        <p className="text-sm text-blue-600 mt-1">
                          📄 Current: Medical Records on file
                        </p>
                      )}
                      <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                    Financial Documents
                  </h3>
                  <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                    <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                    <div className="mt-4">
                      <div className="relative">
                        <input
                          type="file"
                          id="financial-documents-upload"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={(e) => handleDocumentUpload('financial_documents', e)}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                          type="button"
                        >
                          {formData.financial_documents ? 'Change Financial Documents' : 'Upload Financial Documents'}
                        </Button>
                      </div>
                      {formData.financial_documents && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ Selected: {formData.financial_documents.name}
                        </p>
                      )}
                      {isEditing && formData.financial_documents_url && !formData.financial_documents && (
                        <p className="text-sm text-blue-600 mt-1">
                          📄 Current: Financial Documents on file
                        </p>
                      )}
                      <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Button */}
      <div className="fixed bottom-8 right-8">
        <Button
          className="bg-indigo-600 hover:bg-indigo-700 shadow-lg"
          onClick={handleSubmit}
          disabled={isLoading}
          size="lg"
        >
          <Save className="h-5 w-5 mr-2" />
          {isLoading ? "Submitting..." : isEditing ? "Update Student" : "Save Student"}
        </Button>
      </div>
    </div>
  )
}