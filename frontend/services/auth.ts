import { BaseApiService } from './base-api';
import { tokenManager } from './token-manager';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'teacher' | 'staff' | 'parent';
  is_active: boolean;
  profile_picture?: string;
  phone_number?: string;
  date_joined: string;
  last_login?: string;
  permissions: string[];
}

export interface LoginCredentials {
  username: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  role: 'teacher' | 'staff' | 'parent';
  phone_number?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
  confirm_password: string;
}

export interface PasswordChange {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface ProfileUpdate {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  profile_picture?: File;
}

export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
}

export interface Role {
  id: number;
  name: string;
  permissions: Permission[];
  description?: string;
}

export class AuthService extends BaseApiService {
  private readonly authEndpoint = '/auth';
  private readonly usersEndpoint = '/auth/users';
  private readonly profileEndpoint = '/auth/profile';
  
  private currentUser: User | null = null;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    super();
    // Register this service with the token manager
    tokenManager.registerService(this);
    this.loadTokensFromStorage();
  }

  // Authentication Methods
  async login(credentials: LoginCredentials) {
    try {
      const response = await this.post<LoginResponse>(`${this.authEndpoint}/login/`, credentials);
      
      if (response.status === 'success' && response.data) {
        const { access_token, refresh_token, user } = response.data;
        
        this.accessToken = access_token;
        this.refreshToken = refresh_token;
        this.currentUser = user;

        // Set token on all service instances
        tokenManager.setAuthToken(access_token);
        this.saveTokensToStorage();
        
        return response;
      }
      
      return response;
    } catch (error) {
      console.error('Login error:', error);
      return {
        status: 'error',
        http_code: 0,
        message: error instanceof Error ? error.message : 'Login failed'
      };
    }
  }

  async logout() {
    try {
      if (this.refreshToken) {
        await this.post(`${this.authEndpoint}/logout/`, {
          refresh_token: this.refreshToken
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearTokens();
    }
  }

  async register(data: RegisterData) {
    return this.post<{ user: User; message: string }>(`${this.authEndpoint}/register/`, data);
  }

  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await this.post<{ access_token: string; expires_in: number }>(
        `${this.authEndpoint}/token/refresh/`,
        { refresh_token: this.refreshToken }
      );

      if (response.status === 'success' && response.data) {
        this.accessToken = response.data.access_token;
        // Set token on all service instances
        tokenManager.setAuthToken(this.accessToken);
        this.saveTokensToStorage();
        return response;
      }

      throw new Error(response.message || 'Token refresh failed');
    } catch (error) {
      this.clearTokens();
      throw error;
    }
  }

  async verifyToken() {
    if (!this.accessToken) {
      return { status: 'error', http_code: 401, message: 'No access token' };
    }

    return this.post(`${this.authEndpoint}/token/verify/`, {
      token: this.accessToken
    });
  }

  // Password Management
  async requestPasswordReset(data: PasswordResetRequest) {
    return this.post(`${this.authEndpoint}/password/reset/`, data);
  }

  async confirmPasswordReset(data: PasswordResetConfirm) {
    return this.post(`${this.authEndpoint}/password/reset/confirm/`, data);
  }

  async changePassword(data: PasswordChange) {
    return this.post(`${this.authEndpoint}/password/change/`, data);
  }

  // Profile Management
  async getProfile() {
    const response = await this.get<User>(`${this.profileEndpoint}/`);
    if (response.status === 'success' && response.data) {
      this.currentUser = response.data;
    }
    return response;
  }

  async updateProfile(data: ProfileUpdate) {
    // Handle file upload for profile picture
    if (data.profile_picture) {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value);
        }
      });

      try {
        const response = await fetch(`${(this as any).baseUrl}${this.profileEndpoint}/`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
          },
          body: formData,
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || `Profile update failed: ${response.status}`);
        }

        if (result) {
          this.currentUser = result;
        }

        return {
          status: 'success',
          http_code: response.status,
          message: 'Profile updated successfully',
          data: result,
        };
      } catch (error) {
        return {
          status: 'error',
          http_code: 0,
          message: error instanceof Error ? error.message : 'Profile update failed',
        };
      }
    } else {
      const response = await this.patch<User>(`${this.profileEndpoint}/`, data);
      if (response.status === 'success' && response.data) {
        this.currentUser = response.data;
      }
      return response;
    }
  }

  // User Management (Admin only)
  async getUsers(params?: {
    role?: string;
    is_active?: boolean;
    search?: string;
    page?: number;
    limit?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.usersEndpoint}/?${query}` : `${this.usersEndpoint}/`;
    return this.get<{ results: User[]; count: number; next?: string; previous?: string }>(url);
  }

  async getUser(id: number) {
    return this.get<User>(`${this.usersEndpoint}/${id}/`);
  }

  async createUser(userData: Omit<RegisterData, 'password_confirm'> & { password: string }) {
    return this.post<User>(`${this.usersEndpoint}/`, userData);
  }

  async updateUser(id: number, userData: Partial<User>) {
    return this.put<User>(`${this.usersEndpoint}/${id}/`, userData);
  }

  async deleteUser(id: number) {
    return this.delete(`${this.usersEndpoint}/${id}/`);
  }

  async activateUser(id: number) {
    return this.patch<User>(`${this.usersEndpoint}/${id}/`, { is_active: true });
  }

  async deactivateUser(id: number) {
    return this.patch<User>(`${this.usersEndpoint}/${id}/`, { is_active: false });
  }

  // Permissions and Roles
  async getUserPermissions(userId?: number) {
    const url = userId 
      ? `${this.usersEndpoint}/${userId}/permissions/`
      : `${this.profileEndpoint}/permissions/`;
    return this.get<Permission[]>(url);
  }

  async getRoles() {
    return this.get<Role[]>(`${this.authEndpoint}/roles/`);
  }

  async assignRole(userId: number, roleId: number) {
    return this.post(`${this.usersEndpoint}/${userId}/assign-role/`, { role_id: roleId });
  }

  async removeRole(userId: number, roleId: number) {
    return this.post(`${this.usersEndpoint}/${userId}/remove-role/`, { role_id: roleId });
  }

  // Session Management
  async getSessions() {
    return this.get<Array<{
      session_key: string;
      ip_address: string;
      user_agent: string;
      created_at: string;
      last_activity: string;
      is_current: boolean;
    }>>(`${this.authEndpoint}/sessions/`);
  }

  async revokeSession(sessionKey: string) {
    return this.delete(`${this.authEndpoint}/sessions/${sessionKey}/`);
  }

  async revokeAllSessions() {
    return this.post(`${this.authEndpoint}/sessions/revoke-all/`, {});
  }

  // Utility Methods
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  isAuthenticated(): boolean {
    return !!this.accessToken && !!this.currentUser;
  }

  hasPermission(permission: string): boolean {
    return this.currentUser?.permissions.includes(permission) || false;
  }

  hasRole(role: string): boolean {
    return this.currentUser?.role === role;
  }

  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  // Private Methods
  private saveTokensToStorage(): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', this.accessToken || '');
      localStorage.setItem('refresh_token', this.refreshToken || '');
      localStorage.setItem('current_user', JSON.stringify(this.currentUser));
    }
  }

  private loadTokensFromStorage(): void {
    if (typeof window !== 'undefined') {
      this.accessToken = localStorage.getItem('access_token');
      this.refreshToken = localStorage.getItem('refresh_token');
      const userStr = localStorage.getItem('current_user');
      
      if (userStr) {
        try {
          this.currentUser = JSON.parse(userStr);
        } catch (error) {
          console.error('Error parsing stored user data:', error);
        }
      }

      if (this.accessToken) {
        // Set token on all service instances
        tokenManager.setAuthToken(this.accessToken);
      }
    }
  }

  private clearTokens(): void {
    this.accessToken = null;
    this.refreshToken = null;
    this.currentUser = null;

    // Remove token from all service instances
    tokenManager.removeAuthToken();

    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('current_user');
    }
  }
}

export const authService = new AuthService();