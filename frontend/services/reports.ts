import { BaseApiService } from './base-api';
import { tokenManager } from './token-manager';

export interface ReportTemplate {
  id?: number;
  name: string;
  description?: string;
  type: 'Student' | 'Academic' | 'Financial' | 'Attendance' | 'Custom';
  template_config: Record<string, any>;
  is_active: boolean;
  created_by?: number;
  created_at?: string;
  updated_at?: string;
}

export interface GeneratedReport {
  id?: number;
  name: string;
  type: string;
  file_url: string;
  file_format: 'PDF' | 'Excel' | 'CSV';
  parameters: Record<string, any>;
  status: 'Generating' | 'Completed' | 'Failed';
  error_message?: string;
  generated_by?: number;
  generated_at?: string;
  expires_at?: string;
}

export interface StudentReport {
  student_info: {
    id: number;
    name: string;
    admission_number: string;
    class: string;
    section: string;
  };
  academic_performance: {
    subjects: Array<{
      subject: string;
      marks: number;
      grade: string;
      rank?: number;
    }>;
    overall_percentage: number;
    overall_grade: string;
    rank: number;
  };
  attendance: {
    total_days: number;
    present_days: number;
    absent_days: number;
    attendance_percentage: number;
  };
  fee_status: {
    total_fees: number;
    paid_amount: number;
    balance: number;
    status: string;
  };
}

export interface ClassReport {
  class_info: {
    class: string;
    section: string;
    academic_year: string;
    total_students: number;
  };
  academic_summary: {
    average_percentage: number;
    pass_percentage: number;
    subject_wise_average: Array<{
      subject: string;
      average: number;
      highest: number;
      lowest: number;
    }>;
  };
  attendance_summary: {
    class_attendance_percentage: number;
    monthly_breakdown: Array<{
      month: string;
      percentage: number;
    }>;
  };
  fee_summary: {
    total_collected: number;
    total_pending: number;
    collection_percentage: number;
  };
}

export interface AttendanceReport {
  period: {
    start_date: string;
    end_date: string;
    total_days: number;
  };
  summary: {
    total_students: number;
    average_attendance: number;
    highest_attendance: number;
    lowest_attendance: number;
  };
  daily_breakdown: Array<{
    date: string;
    present: number;
    absent: number;
    percentage: number;
  }>;
  student_wise: Array<{
    student_id: number;
    student_name: string;
    present_days: number;
    absent_days: number;
    percentage: number;
    status: string;
  }>;
}

export interface FinancialReport {
  period: {
    start_date: string;
    end_date: string;
  };
  summary: {
    total_collection: number;
    total_pending: number;
    collection_percentage: number;
    total_transactions: number;
  };
  monthly_breakdown: Array<{
    month: string;
    collected: number;
    pending: number;
    transactions: number;
  }>;
  fee_type_breakdown: Array<{
    fee_type: string;
    collected: number;
    pending: number;
    percentage: number;
  }>;
  class_wise_collection: Array<{
    class: string;
    collected: number;
    pending: number;
    percentage: number;
  }>;
}

export class ReportsService extends BaseApiService {
  private readonly reportsEndpoint = '/reports';
  private readonly templatesEndpoint = '/reports/templates';
  private readonly generateEndpoint = '/reports/generate';

  constructor() {
    super();
    // Register this service with the token manager
    tokenManager.registerService(this);
  }

  // Report Templates
  async getReportTemplates(type?: string) {
    const url = type ? `${this.templatesEndpoint}/?type=${type}` : `${this.templatesEndpoint}/`;
    return this.get<ReportTemplate[]>(url);
  }

  async getReportTemplate(id: number) {
    return this.get<ReportTemplate>(`${this.templatesEndpoint}/${id}/`);
  }

  async createReportTemplate(template: Omit<ReportTemplate, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<ReportTemplate>(`${this.templatesEndpoint}/`, template);
  }

  async updateReportTemplate(id: number, template: Partial<ReportTemplate>) {
    return this.put<ReportTemplate>(`${this.templatesEndpoint}/${id}/`, template);
  }

  async deleteReportTemplate(id: number) {
    return this.delete(`${this.templatesEndpoint}/${id}/`);
  }

  // Generated Reports
  async getGeneratedReports(params?: {
    type?: string;
    status?: string;
    date_from?: string;
    date_to?: string;
    generated_by?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const query = queryParams.toString();
    const url = query ? `${this.reportsEndpoint}/?${query}` : `${this.reportsEndpoint}/`;
    return this.get<GeneratedReport[]>(url);
  }

  async getGeneratedReport(id: number) {
    return this.get<GeneratedReport>(`${this.reportsEndpoint}/${id}/`);
  }

  async deleteGeneratedReport(id: number) {
    return this.delete(`${this.reportsEndpoint}/${id}/`);
  }

  async downloadReport(id: number) {
    return this.get<{ download_url: string }>(`${this.reportsEndpoint}/${id}/download/`);
  }

  // Report Generation
  async generateStudentReport(
    studentId: number,
    academicYear: string,
    format: 'PDF' | 'Excel' = 'PDF'
  ) {
    return this.post<GeneratedReport>(`${this.generateEndpoint}/student/`, {
      student_id: studentId,
      academic_year: academicYear,
      format,
    });
  }

  async generateClassReport(
    classId: number,
    academicYear: string,
    reportType: 'academic' | 'attendance' | 'fees' | 'comprehensive' = 'comprehensive',
    format: 'PDF' | 'Excel' = 'PDF'
  ) {
    return this.post<GeneratedReport>(`${this.generateEndpoint}/class/`, {
      class_id: classId,
      academic_year: academicYear,
      report_type: reportType,
      format,
    });
  }

  async generateAttendanceReport(
    params: {
      class_id?: number;
      student_id?: number;
      start_date: string;
      end_date: string;
      format?: 'PDF' | 'Excel';
    }
  ) {
    return this.post<GeneratedReport>(`${this.generateEndpoint}/attendance/`, params);
  }

  async generateFinancialReport(
    params: {
      start_date: string;
      end_date: string;
      class_id?: number;
      fee_type?: string;
      format?: 'PDF' | 'Excel';
    }
  ) {
    return this.post<GeneratedReport>(`${this.generateEndpoint}/financial/`, params);
  }

  async generateCustomReport(
    templateId: number,
    parameters: Record<string, any>,
    format: 'PDF' | 'Excel' = 'PDF'
  ) {
    return this.post<GeneratedReport>(`${this.generateEndpoint}/custom/`, {
      template_id: templateId,
      parameters,
      format,
    });
  }

  // Quick Report Data (for dashboard/preview)
  async getStudentReportData(studentId: number, academicYear: string) {
    return this.get<StudentReport>(`${this.reportsEndpoint}/data/student/?student_id=${studentId}&academic_year=${academicYear}`);
  }

  async getClassReportData(classId: number, academicYear: string) {
    return this.get<ClassReport>(`${this.reportsEndpoint}/data/class/?class_id=${classId}&academic_year=${academicYear}`);
  }

  async getAttendanceReportData(params: {
    class_id?: number;
    student_id?: number;
    start_date: string;
    end_date: string;
  }) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    return this.get<AttendanceReport>(`${this.reportsEndpoint}/data/attendance/?${queryParams.toString()}`);
  }

  async getFinancialReportData(params: {
    start_date: string;
    end_date: string;
    class_id?: number;
    fee_type?: string;
  }) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    return this.get<FinancialReport>(`${this.reportsEndpoint}/data/financial/?${queryParams.toString()}`);
  }

  // Analytics and Dashboard Data
  async getDashboardStats() {
    return this.get<{
      total_students: number;
      total_classes: number;
      attendance_today: number;
      fees_collected_this_month: number;
      pending_fees: number;
      recent_reports: GeneratedReport[];
    }>(`${this.reportsEndpoint}/dashboard-stats/`);
  }

  async getMonthlyTrends(year: number, month: number) {
    return this.get<{
      attendance_trend: Array<{ date: string; percentage: number }>;
      fee_collection_trend: Array<{ date: string; amount: number }>;
      enrollment_trend: Array<{ date: string; count: number }>;
    }>(`${this.reportsEndpoint}/monthly-trends/?year=${year}&month=${month}`);
  }

  // Bulk Operations
  async generateBulkReports(
    reportType: 'student' | 'class',
    ids: number[],
    academicYear: string,
    format: 'PDF' | 'Excel' = 'PDF'
  ) {
    return this.post<{ job_id: string; status: string }>(`${this.generateEndpoint}/bulk/`, {
      report_type: reportType,
      ids,
      academic_year: academicYear,
      format,
    });
  }

  async getBulkReportStatus(jobId: string) {
    return this.get<{
      job_id: string;
      status: 'pending' | 'processing' | 'completed' | 'failed';
      progress: number;
      completed_reports: GeneratedReport[];
      failed_reports: Array<{ id: number; error: string }>;
    }>(`${this.reportsEndpoint}/bulk-status/${jobId}/`);
  }

  // Report Scheduling
  async scheduleReport(
    reportConfig: {
      template_id?: number;
      report_type: string;
      parameters: Record<string, any>;
      schedule: {
        frequency: 'daily' | 'weekly' | 'monthly';
        time: string;
        day_of_week?: number;
        day_of_month?: number;
      };
      recipients: string[];
    }
  ) {
    return this.post<{ schedule_id: string }>(`${this.reportsEndpoint}/schedule/`, reportConfig);
  }

  async getScheduledReports() {
    return this.get<Array<{
      id: string;
      report_type: string;
      schedule: any;
      recipients: string[];
      is_active: boolean;
      next_run: string;
    }>>(`${this.reportsEndpoint}/scheduled/`);
  }

  async updateScheduledReport(scheduleId: string, config: any) {
    return this.put(`${this.reportsEndpoint}/scheduled/${scheduleId}/`, config);
  }

  async deleteScheduledReport(scheduleId: string) {
    return this.delete(`${this.reportsEndpoint}/scheduled/${scheduleId}/`);
  }
}

export const reportsService = new ReportsService();