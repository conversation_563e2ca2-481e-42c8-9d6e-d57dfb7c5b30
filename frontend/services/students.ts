import { BaseApiService } from './base-api';
import { tokenManager } from './token-manager';

export interface Student {
  id?: number;
  student_id?: string;
  first_name: string;
  last_name: string;
  father_name?: string;
  date_of_birth: string;
  gender: 'M' | 'F' | 'O';
  
  // Pakistani Identity Documents
  cnic?: string;
  b_form_number?: string;
  
  // Contact Information
  phone_number?: string;
  email?: string;
  
  // Address (Pakistani format)
  address_line_1?: string;
  address_line_2?: string;
  address?: string; // Legacy field for backward compatibility
  city?: string;
  province?: string;
  postal_code?: string;
  
  // Personal Details
  religion?: string;
  nationality?: string;
  marital_status?: string;
  blood_group?: string;
  
  // Photo
  photo?: string; // URL to the student photo
  
  // Medical Information
  medical_conditions?: string;
  allergies?: string;
  emergency_medical_contact?: string;
  
  // Academic Information
  previous_education?: string;
  matric_marks?: number;
  intermediate_marks?: number;
  
  // Legacy academic fields
  class_level?: string;
  section?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_email?: string;
  guardian_cnic?: string;
  guardian_relationship?: string;
  guardian_occupation?: string;
  academic_year?: string;
  term?: string;
  grade?: string;
  previous_school?: string;
  height?: string;
  weight?: string;
  emergency_contact?: string;
  
  // System Fields
  is_active: boolean;
  admission_date: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
}

export interface StudentListParams {
  page?: number;
  limit?: number;
  search?: string;
  class_level?: string;
  section?: string;
  is_active?: boolean;
}

export interface StudentListResponse {
  results: Student[];
  count: number;
  next?: string;
  previous?: string;
}

export class StudentsService extends BaseApiService {
  private readonly endpoint = '/students';

  constructor() {
    super();
    // Register this service with the token manager
    tokenManager.registerService(this);
  }

  async getStudents(params?: StudentListParams) {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const query = queryParams.toString();
    const url = query ? `${this.endpoint}/?${query}` : `${this.endpoint}/`;
    
    return this.get<StudentListResponse>(url);
  }

  async getStudent(id: number) {
    return this.get<Student>(`${this.endpoint}/${id}/`);
  }

  async getById(id: number) {
    return this.getStudent(id);
  }

  async createStudent(student: Omit<Student, 'id' | 'created_at' | 'updated_at'>) {
    return this.post<Student>(`${this.endpoint}/`, student);
  }

  async createStudentWithFormData(formData: FormData) {
    return this.postFormData<Student>(`${this.endpoint}/`, formData);
  }

  async updateStudent(id: number, student: Partial<Student>) {
    return this.put<Student>(`${this.endpoint}/${id}/`, student);
  }

  async updateStudentWithFormData(id: number, formData: FormData) {
    return this.putFormData<Student>(`${this.endpoint}/${id}/`, formData);
  }

  async patchStudent(id: number, student: Partial<Student>) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, student);
  }

  async deleteStudent(id: number) {
    return this.delete(`${this.endpoint}/${id}/`);
  }

  async bulkImportStudents(studentsData: Omit<Student, 'id' | 'created_at' | 'updated_at'>[]) {
    return this.post<{ success: number; errors: any[] }>(`${this.endpoint}/bulk-import/`, {
      students: studentsData
    });
  }

  async getStudentsByClass(classLevel: string, section?: string) {
    const params: StudentListParams = { class_level: classLevel };
    if (section) params.section = section;
    return this.getStudents(params);
  }

  async searchStudents(query: string) {
    return this.getStudents({ search: query });
  }

  async activateStudent(id: number) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, { is_active: true });
  }

  async deactivateStudent(id: number) {
    return this.patch<Student>(`${this.endpoint}/${id}/`, { is_active: false });
  }
}

export const studentsService = new StudentsService();