// Centralized token management to avoid circular imports
import { BaseApiService } from './base-api';

class TokenManager {
  private services: BaseApiService[] = [];

  registerService(service: BaseApiService): void {
    this.services.push(service);

    // If there's already a token in localStorage, set it on the newly registered service
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('access_token');
      if (token) {
        service.setAuthToken(token);
      }
    }
  }

  setAuthToken(token: string): void {
    this.services.forEach(service => {
      service.setAuthToken(token);
    });
  }

  removeAuthToken(): void {
    this.services.forEach(service => {
      service.removeAuthToken();
    });
  }
}

export const tokenManager = new TokenManager();
