"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  BarChart3,
  Download,
  FileText,
  TrendingUp,
  Users,
  DollarSign,
  Calendar,
  PieChart,
  LineChart,
  Activity,
} from "lucide-react"

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState("financial")

  const reportTypes = [
    {
      name: "Student Enrollment Report",
      description: "Comprehensive student enrollment data and trends",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      name: "Fee Collection Report",
      description: "Financial collection and outstanding fees analysis",
      icon: DollarSign,
      color: "bg-green-500",
    },
    {
      name: "Academic Performance Report",
      description: "Student grades and academic progress tracking",
      icon: BarChart3,
      color: "bg-purple-500",
    },
    {
      name: "Attendance Report",
      description: "Student and staff attendance records",
      icon: Calendar,
      color: "bg-orange-500",
    },
    {
      name: "Class Roster Report",
      description: "Class-wise student listings and details",
      icon: FileText,
      color: "bg-indigo-500",
    },
    {
      name: "Financial Summary Report",
      description: "Complete financial overview and analytics",
      icon: TrendingUp,
      color: "bg-emerald-500",
    },
  ]

  const quickStats = [
    { title: "Reports Generated", value: "1,247", change: "+23%", icon: FileText },
    { title: "Data Points", value: "45.2K", change: "+12%", icon: Activity },
    { title: "Export Downloads", value: "892", change: "+8%", icon: Download },
    { title: "Scheduled Reports", value: "34", change: "+5%", icon: Calendar },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-1">Generate comprehensive reports and analyze institutional data</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Report
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <Download className="h-4 w-4 mr-2" />
            Export All
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                  <p className="text-sm text-green-600 mt-1">{stat.change} this month</p>
                </div>
                <div className="p-3 bg-indigo-100 rounded-full">
                  <stat.icon className="h-5 w-5 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <TabsList className="grid w-full grid-cols-3 bg-indigo-50">
            <TabsTrigger value="financial" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Financial Reports
            </TabsTrigger>
            <TabsTrigger value="academic" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Academic Reports
            </TabsTrigger>
            <TabsTrigger
              value="administrative"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              Administrative Reports
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Report Generator */}
            <Card className="lg:col-span-2 border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                <CardTitle className="text-xl text-green-900 flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Financial Report Generator
                </CardTitle>
                <CardDescription>Generate detailed financial reports and summaries</CardDescription>
              </CardHeader>
              <CardContent className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="reportType" className="text-sm font-semibold text-gray-700">
                      Report Type
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fee-collection">Fee Collection Summary</SelectItem>
                        <SelectItem value="outstanding-fees">Outstanding Fees</SelectItem>
                        <SelectItem value="payment-history">Payment History</SelectItem>
                        <SelectItem value="scholarship-report">Scholarship Report</SelectItem>
                        <SelectItem value="refund-report">Refund Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateRange" className="text-sm font-semibold text-gray-700">
                      Date Range
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="today">Today</SelectItem>
                        <SelectItem value="week">This Week</SelectItem>
                        <SelectItem value="month">This Month</SelectItem>
                        <SelectItem value="quarter">This Quarter</SelectItem>
                        <SelectItem value="year">This Year</SelectItem>
                        <SelectItem value="custom">Custom Range</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateFrom" className="text-sm font-semibold text-gray-700">
                      From Date
                    </Label>
                    <Input
                      id="dateFrom"
                      type="date"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateTo" className="text-sm font-semibold text-gray-700">
                      To Date
                    </Label>
                    <Input
                      id="dateTo"
                      type="date"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                </div>
                <div className="flex flex-wrap gap-3">
                  <Button className="bg-indigo-600 hover:bg-indigo-700">Generate Report</Button>
                  <Button
                    variant="outline"
                    className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                  >
                    Preview
                  </Button>
                  <Button
                    variant="outline"
                    className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export PDF
                  </Button>
                  <Button
                    variant="outline"
                    className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Excel
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Financial Summary */}
            <div className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                  <CardTitle className="text-lg text-green-900">Daily Collection</CardTitle>
                  <CardDescription>Today's fee collection</CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="text-3xl font-bold text-green-600 mb-2">$12,450</div>
                  <p className="text-sm text-gray-600 mb-4">45 payments received</p>
                  <Progress value={75} className="h-2 mb-4" />
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full border-green-200 text-green-700 hover:bg-green-50 bg-transparent"
                  >
                    View Details
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg">
                <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 border-b">
                  <CardTitle className="text-lg text-red-900">Outstanding Fees</CardTitle>
                  <CardDescription>Pending payments</CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="text-3xl font-bold text-red-600 mb-2">$45,230</div>
                  <p className="text-sm text-gray-600 mb-4">156 students</p>
                  <Progress value={35} className="h-2 mb-4" />
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full border-red-200 text-red-700 hover:bg-red-50 bg-transparent"
                  >
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Financial Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                <CardTitle className="text-lg text-blue-900">Monthly Revenue</CardTitle>
                <CardDescription>Revenue trends over time</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">$245,680</div>
                <p className="text-sm text-green-600 mb-4">+12% from last month</p>
                <div className="flex items-center space-x-2">
                  <LineChart className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-gray-600">View Trends</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b">
                <CardTitle className="text-lg text-purple-900">Fee Categories</CardTitle>
                <CardDescription>Revenue by category</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Tuition</span>
                    <span className="font-semibold">65%</span>
                  </div>
                  <Progress value={65} className="h-2" />
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Transport</span>
                    <span className="font-semibold">20%</span>
                  </div>
                  <Progress value={20} className="h-2" />
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Other</span>
                    <span className="font-semibold">15%</span>
                  </div>
                  <Progress value={15} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-green-50 border-b">
                <CardTitle className="text-lg text-emerald-900">Collection Rate</CardTitle>
                <CardDescription>Payment efficiency</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-emerald-600 mb-2">87.5%</div>
                <p className="text-sm text-gray-600 mb-4">Average collection rate</p>
                <div className="flex items-center space-x-2">
                  <PieChart className="h-4 w-4 text-emerald-600" />
                  <span className="text-sm text-gray-600">View Breakdown</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="academic" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Academic Report Generator
              </CardTitle>
              <CardDescription>Generate academic performance and enrollment reports</CardDescription>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="academicReportType" className="text-sm font-semibold text-gray-700">
                    Report Type
                  </Label>
                  <Select>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="enrollment">Student Enrollment</SelectItem>
                      <SelectItem value="class-roster">Class Rosters</SelectItem>
                      <SelectItem value="performance">Academic Performance</SelectItem>
                      <SelectItem value="attendance">Attendance Summary</SelectItem>
                      <SelectItem value="graduation">Graduation Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="academicYear" className="text-sm font-semibold text-gray-700">
                    Academic Year
                  </Label>
                  <Select>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select year" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2024-2025">2024-2025</SelectItem>
                      <SelectItem value="2023-2024">2023-2024</SelectItem>
                      <SelectItem value="2022-2023">2022-2023</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="class" className="text-sm font-semibold text-gray-700">
                    Class/Grade
                  </Label>
                  <Select>
                    <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      <SelectItem value="grade-9">Grade 9</SelectItem>
                      <SelectItem value="grade-10">Grade 10</SelectItem>
                      <SelectItem value="grade-11">Grade 11</SelectItem>
                      <SelectItem value="grade-12">Grade 12</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button className="bg-indigo-600 hover:bg-indigo-700">Generate Report</Button>
                <Button
                  variant="outline"
                  className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                >
                  Preview
                </Button>
                <Button
                  variant="outline"
                  className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Excel
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="administrative" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Administrative Reports</CardTitle>
              <CardDescription>Generate administrative and operational reports</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reportTypes.map((report, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer border border-gray-200">
                    <CardHeader className="pb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-full ${report.color}`}>
                          <report.icon className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-lg text-gray-900">{report.name}</CardTitle>
                        </div>
                      </div>
                      <CardDescription className="mt-2">{report.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex space-x-2">
                        <Button size="sm" className="flex-1 bg-indigo-600 hover:bg-indigo-700">
                          Generate
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
