"use client"

import { useState, useEffect } from "react"
import { useR<PERSON>er, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { studentsService, type Student } from "@/services/students"
import StudentForm, { type StudentFormData } from "@/components/student-form"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

export default function EditStudentPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingStudent, setIsLoadingStudent] = useState(true)
  const [studentData, setStudentData] = useState<Partial<StudentFormData> | null>(null)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  // Load existing student data
  useEffect(() => {
    const loadStudentData = async () => {
      if (!params.id) return
      
      try {
        setIsLoadingStudent(true)
        const response = await studentsService.getById(Number(params.id))
        
        if (response.status === 'success' && response.data) {
          const student = response.data
          
          // Map student data to form structure
          const mappedData: Partial<StudentFormData> = {
            // Basic Info
            first_name: student.first_name || "",
            last_name: student.last_name || "",
            father_name: student.father_name || "",
            date_of_birth: student.date_of_birth || "",
            gender: student.gender === 'M' ? 'male' : student.gender === 'F' ? 'female' : student.gender === 'O' ? 'other' : "",
            nationality: student.nationality || "",
            religion: student.religion || "",
            marital_status: student.marital_status || "",
            photoPreview: student.photo || "",

            // Pakistani Identity Documents
            cnic_number: student.cnic || "",
            b_form_number: student.b_form_number || "",

            // Contact Information
            phone_number: student.phone_number || "",
            email: student.email || "",

            // Address (Pakistani format)
            address_line_1: student.address_line_1 || student.address || "",
            address_line_2: student.address_line_2 || "",
            address: student.address || "",
            city: student.city || "",
            province: student.province || "",
            postal_code: student.postal_code || "",

            // Personal Details
            blood_group: student.blood_group || "",

            // Medical Information
            medical_conditions: student.medical_conditions || "",
            allergies: student.allergies || "",
            emergency_medical_contact: student.emergency_medical_contact || "",

            // Academic Information  
            previous_education: student.previous_education || "",
            matric_marks: student.matric_marks?.toString() || "",
            intermediate_marks: student.intermediate_marks?.toString() || "",

            // Guardian Info
            guardian_name: student.guardian_name || "",
            guardian_phone: student.guardian_phone || "",
            guardian_email: student.guardian_email || "",
            guardian_cnic: student.guardian_cnic || "",
            guardian_relationship: student.guardian_relationship || "",
            guardian_occupation: student.guardian_occupation || "",
            guardian_address: "",

            // Academic Info
            academic_year: student.academic_year || "",
            term: student.term || "",
            grade: student.grade || "",
            section: student.section || "",
            class_level: student.class_level || "",
            admission_date: student.admission_date || "",
            previous_school: student.previous_school || "",

            // Medical Info (additional)
            emergency_contact: student.emergency_contact || "",
            emergency_phone: "",
            height: student.height || "",
            weight: student.weight || "",

            // Additional fields
            is_active: student.is_active !== false,

            // File fields remain null initially (for new uploads)
            photo: null,
            birth_certificate: null,
            academic_transcripts: null,
            medical_records: null,
            financial_documents: null,

            // Document URLs (for existing documents display)
            birth_certificate_url: "",
            academic_transcripts_url: "", 
            medical_records_url: "",
            financial_documents_url: ""
          }
          
          setStudentData(mappedData)
        } else {
          toast({
            title: "Error",
            description: "Failed to load student data",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error('Error loading student:', error)
        toast({
          title: "Error",
          description: "Failed to load student data",
          variant: "destructive",
        })
      } finally {
        setIsLoadingStudent(false)
      }
    }

    loadStudentData()
  }, [params.id, toast])

  const handleUpdateStudent = async (formData: StudentFormData) => {
    if (!params.id) return
    
    setIsLoading(true)
    try {
      // Create FormData for multipart form submission including photo and documents
      const formDataToSend = new FormData()
      
      // Add all text fields
      formDataToSend.append('first_name', formData.first_name)
      formDataToSend.append('last_name', formData.last_name)
      formDataToSend.append('father_name', formData.father_name || '')
      formDataToSend.append('date_of_birth', formData.date_of_birth)
      formDataToSend.append('gender', formData.gender === 'male' ? 'M' : formData.gender === 'female' ? 'F' : 'O')
      formDataToSend.append('phone_number', formData.phone_number || '+923001234567')
      formDataToSend.append('email', formData.email || '')
      formDataToSend.append('address_line_1', formData.address_line_1 || formData.address || '')
      formDataToSend.append('address_line_2', formData.address_line_2 || '')
      formDataToSend.append('city', formData.city || '')
      formDataToSend.append('province', formData.province || '')
      formDataToSend.append('postal_code', formData.postal_code || '')
      formDataToSend.append('nationality', formData.nationality || 'Pakistani')
      formDataToSend.append('religion', formData.religion || 'Islam')  
      formDataToSend.append('marital_status', formData.marital_status || 'S')
      formDataToSend.append('is_active', formData.is_active ? 'true' : 'false')
      
      // Add optional fields
      if (formData.cnic_number) formDataToSend.append('cnic', formData.cnic_number)
      if (formData.b_form_number) formDataToSend.append('b_form_number', formData.b_form_number)
      if (formData.blood_group) formDataToSend.append('blood_group', formData.blood_group)
      if (formData.medical_conditions) formDataToSend.append('medical_conditions', formData.medical_conditions)
      if (formData.allergies) formDataToSend.append('allergies', formData.allergies)
      if (formData.emergency_medical_contact) formDataToSend.append('emergency_medical_contact', formData.emergency_medical_contact)
      if (formData.previous_education) formDataToSend.append('previous_education', formData.previous_education)
      if (formData.matric_marks) formDataToSend.append('matric_marks', formData.matric_marks)
      if (formData.intermediate_marks) formDataToSend.append('intermediate_marks', formData.intermediate_marks)
      if (formData.guardian_name) formDataToSend.append('guardian_name', formData.guardian_name)
      if (formData.guardian_phone) formDataToSend.append('guardian_phone', formData.guardian_phone)
      if (formData.guardian_email) formDataToSend.append('guardian_email', formData.guardian_email)
      if (formData.guardian_cnic) formDataToSend.append('guardian_cnic', formData.guardian_cnic)
      if (formData.guardian_relationship) formDataToSend.append('guardian_relationship', formData.guardian_relationship)
      if (formData.guardian_occupation) formData.guardian_occupation && formDataToSend.append('guardian_occupation', formData.guardian_occupation)
      if (formData.academic_year) formDataToSend.append('academic_year', formData.academic_year)
      if (formData.term) formDataToSend.append('term', formData.term)
      if (formData.grade) formDataToSend.append('grade', formData.grade)
      if (formData.section) formDataToSend.append('section', formData.section)
      if (formData.class_level) formDataToSend.append('class_level', formData.class_level)
      if (formData.previous_school) formDataToSend.append('previous_school', formData.previous_school)
      if (formData.previous_education) formDataToSend.append('previous_education', formData.previous_education)
      if (formData.matric_marks) formDataToSend.append('matric_marks', formData.matric_marks)
      if (formData.intermediate_marks) formDataToSend.append('intermediate_marks', formData.intermediate_marks)
      if (formData.height) formDataToSend.append('height', formData.height)
      if (formData.weight) formDataToSend.append('weight', formData.weight)
      if (formData.emergency_contact) formDataToSend.append('emergency_contact', formData.emergency_contact)
      if (formData.admission_date) formDataToSend.append('admission_date', formData.admission_date)
      
      // Add photo if selected
      if (formData.photo) {
        formDataToSend.append('photo', formData.photo)
      }
      
      // Add documents if selected
      if (formData.birth_certificate) {
        formDataToSend.append('birth_certificate', formData.birth_certificate)
      }
      if (formData.academic_transcripts) {
        formDataToSend.append('academic_transcripts', formData.academic_transcripts)
      }
      if (formData.medical_records) {
        formDataToSend.append('medical_records', formData.medical_records)
      }
      if (formData.financial_documents) {
        formDataToSend.append('financial_documents', formData.financial_documents)
      }

      const response = await studentsService.updateStudentWithFormData(Number(params.id), formDataToSend)

      if (response.status === 'success') {
        toast({
          title: "Success",
          description: `Student updated successfully! ID: ${response.data?.student_id || 'Updated'}`,
        })
        router.push('/students')
      } else {
        // Handle field-specific validation errors
        if (response.errors && typeof response.errors === 'object') {
          const errorMessages = Object.entries(response.errors).map(([field, errors]) => {
            const errorArray = Array.isArray(errors) ? errors : [errors]
            const fieldLabels: Record<string, string> = {
              first_name: 'First Name',
              last_name: 'Last Name',
              cnic: 'CNIC',
              guardian_cnic: 'Guardian CNIC',
              guardian_phone: 'Guardian Phone',
              guardian_email: 'Guardian Email',
              date_of_birth: 'Date of Birth',
              phone_number: 'Phone Number',
              email: 'Email'
            }
            return `${fieldLabels[field] || field}: ${errorArray[0]}`
          })
          
          const errorCount = Object.keys(response.errors).length
          toast({
            title: "Validation Error",
            description: errorCount === 1 
              ? errorMessages[0]
              : `${errorCount} errors found:\n${errorMessages.slice(0, 3).join('\n')}${errorCount > 3 ? `\n...and ${errorCount - 3} more` : ''}`,
            variant: "destructive",
          })
        } else {
          throw new Error(response.message || 'Failed to update student')
        }
      }
    } catch (error) {
      console.error('Error updating student:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update student",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingStudent) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/students">
              <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Students
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Student</h1>
              <p className="text-gray-600 mt-1">Loading student information...</p>
            </div>
          </div>
        </div>
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!studentData) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/students">
              <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Students
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Student</h1>
              <p className="text-gray-600 mt-1">Student not found</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/students">
            <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Students
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Student</h1>
            <p className="text-gray-600 mt-1">Update student profile information</p>
          </div>
        </div>
      </div>

      <StudentForm 
        initialData={studentData}
        isEditing={true}
        onSubmit={handleUpdateStudent}
        isLoading={isLoading}
      />
    </div>
  )
}