"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Edit, Printer, User, Phone, Mail, MapPin, Calendar, Users, Heart, FileText, GraduationCap } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { studentsService, type Student } from "@/services/students"
import Link from "next/link"

export default function StudentProfilePage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [student, setStudent] = useState<Student | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchStudent = async () => {
      if (!params.id) return
      
      try {
        setIsLoading(true)
        const response = await studentsService.getById(Number(params.id))
        if (response.status === 'success' && response.data) {
          setStudent(response.data)
        } else {
          toast({
            title: "Error",
            description: "Failed to load student profile",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error('Error fetching student:', error)
        toast({
          title: "Error",
          description: "Failed to load student profile",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchStudent()
  }, [params.id, toast])

  const handlePrint = () => {
    window.print()
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!student) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Student Not Found</h1>
          <p className="text-gray-600 mb-6">The requested student profile could not be found.</p>
          <Link href="/students">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Students
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          .no-print { display: none !important; }
          .print-only { display: block !important; }
          body { font-size: 12pt; }
          .container { max-width: none !important; margin: 0 !important; padding: 0 !important; }
        }
        .print-only { display: none; }
      `}</style>

      <div className="p-6 bg-gray-50 min-h-screen no-print">
        {/* Header - Hidden in print */}
        <div className="max-w-4xl mx-auto mb-6 no-print">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/students">
                <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Students
                </Button>
              </Link>
            </div>
            <div className="flex items-center space-x-3">
              <Button 
                variant="outline" 
                onClick={handlePrint}
                className="border-gray-300"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Profile
              </Button>
              <Link href={`/students/${params.id}/edit`}>
                <Button className="bg-indigo-600 hover:bg-indigo-700">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Student
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Print Header - Only visible in print */}
        <div className="print-only text-center mb-8 border-b-2 border-indigo-600 pb-4">
          <h1 className="text-2xl font-bold text-indigo-900">GIHD School Management System</h1>
          <p className="text-gray-600">Student Profile Report</p>
          <p className="text-sm text-gray-500">Generated on {new Date().toLocaleDateString()}</p>
        </div>

        {/* Main Profile Content */}
        <div className="max-w-4xl mx-auto container">
          {/* Header Card */}
          <Card className="mb-6 border-0 shadow-lg">
            <CardContent className="p-8">
              <div className="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
                {/* Profile Photo */}
                <div className="flex-shrink-0">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-indigo-100 shadow-lg">
                    <img
                      src={student.photo || `https://ui-avatars.com/api/?name=${encodeURIComponent(
                        `${student.first_name} ${student.last_name}`
                      )}&background=6366f1&color=ffffff&size=128`}
                      alt={`${student.first_name} ${student.last_name}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(
                          `${student.first_name} ${student.last_name}`
                        )}&background=6366f1&color=ffffff&size=128`;
                      }}
                    />
                  </div>
                </div>

                {/* Basic Info */}
                <div className="flex-1">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900">
                        {student.first_name} {student.last_name}
                      </h1>
                      <p className="text-lg text-indigo-600 font-medium">
                        Student ID: {student.student_id || 'Not assigned'}
                      </p>
                    </div>
                    <div className="mt-2 md:mt-0">
                      <Badge 
                        variant={student.is_active ? "default" : "secondary"}
                        className={`text-sm px-3 py-1 ${
                          student.is_active 
                            ? "bg-green-100 text-green-800 border-green-200" 
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {student.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="text-center">
                      <Calendar className="h-5 w-5 mx-auto text-indigo-600 mb-1" />
                      <p className="font-medium text-gray-900">Joined</p>
                      <p className="text-gray-600">{new Date(student.admission_date).toLocaleDateString()}</p>
                    </div>
                    <div className="text-center">
                      <GraduationCap className="h-5 w-5 mx-auto text-indigo-600 mb-1" />
                      <p className="font-medium text-gray-900">Class</p>
                      <p className="text-gray-600">
                        {student.class_level && student.section 
                          ? `${student.class_level}-${student.section}` 
                          : 'Not assigned'
                        }
                      </p>
                    </div>
                    <div className="text-center">
                      <User className="h-5 w-5 mx-auto text-indigo-600 mb-1" />
                      <p className="font-medium text-gray-900">Gender</p>
                      <p className="text-gray-600">
                        {student.gender === 'M' ? 'Male' : student.gender === 'F' ? 'Female' : 'Other'}
                      </p>
                    </div>
                    <div className="text-center">
                      <Users className="h-5 w-5 mx-auto text-indigo-600 mb-1" />
                      <p className="font-medium text-gray-900">Age</p>
                      <p className="text-gray-600">
                        {new Date().getFullYear() - new Date(student.date_of_birth).getFullYear()} years
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="flex items-center text-indigo-900">
                  <User className="h-5 w-5 mr-2" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Date of Birth</label>
                    <p className="text-gray-900">{new Date(student.date_of_birth).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Nationality</label>
                    <p className="text-gray-900">{student.nationality || 'Not specified'}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Religion</label>
                  <p className="text-gray-900">{student.religion || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">CNIC/ID Number</label>
                  <p className="text-gray-900 font-mono">{student.cnic || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                <CardTitle className="flex items-center text-green-900">
                  <Phone className="h-5 w-5 mr-2" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div>
                  <label className="text-sm font-semibold text-gray-600">Phone Number</label>
                  <p className="text-gray-900 font-mono">{student.phone_number || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Email Address</label>
                  <p className="text-gray-900">{student.email || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Address</label>
                  <p className="text-gray-900">{student.address || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Guardian Information */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 border-b">
                <CardTitle className="flex items-center text-purple-900">
                  <Users className="h-5 w-5 mr-2" />
                  Guardian Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div>
                  <label className="text-sm font-semibold text-gray-600">Guardian Name</label>
                  <p className="text-gray-900">{student.guardian_name || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Guardian Phone</label>
                  <p className="text-gray-900 font-mono">{student.guardian_phone || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Guardian Email</label>
                  <p className="text-gray-900">{student.guardian_email || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Academic Information */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 border-b">
                <CardTitle className="flex items-center text-orange-900">
                  <GraduationCap className="h-5 w-5 mr-2" />
                  Academic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Class Level</label>
                    <p className="text-gray-900">{student.class_level || 'Not assigned'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Section</label>
                    <p className="text-gray-900">{student.section || 'Not assigned'}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Admission Date</label>
                  <p className="text-gray-900">{new Date(student.admission_date).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-600">Student Status</label>
                  <Badge 
                    variant={student.is_active ? "default" : "secondary"}
                    className={student.is_active 
                      ? "bg-green-100 text-green-800 border-green-200" 
                      : "bg-gray-100 text-gray-800"
                    }
                  >
                    {student.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Print Footer */}
          <div className="print-only mt-12 pt-6 border-t border-gray-300 text-center text-sm text-gray-600">
            <p>This is an official document generated from GIHD School Management System</p>
            <p>Generated on {new Date().toLocaleString()} | Student ID: {student.student_id}</p>
          </div>
        </div>
      </div>
    </>
  )
}