"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { studentsService } from "@/services/students"
import StudentForm, { type StudentFormData } from "@/components/student-form"
import Link from "next/link"

export default function NewStudentPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleSaveStudent = async (formData: StudentFormData) => {
    setIsLoading(true)
    try {
      // Create FormData for multipart form submission including photo
      const formDataToSend = new FormData()
      
      // Add all text fields
      formDataToSend.append('first_name', formData.first_name)
      formDataToSend.append('last_name', formData.last_name)
      formDataToSend.append('father_name', formData.guardian_relationship === 'father' ? formData.guardian_name : 'N/A')
      formDataToSend.append('date_of_birth', formData.date_of_birth)
      formDataToSend.append('gender', formData.gender === 'male' ? 'M' : formData.gender === 'female' ? 'F' : 'M')
      formDataToSend.append('phone_number', formData.guardian_phone || formData.phone_number || '+923001234567')
      formDataToSend.append('email', formData.guardian_email || formData.email || '')
      formDataToSend.append('address_line_1', formData.address || 'N/A')
      formDataToSend.append('city', formData.city || 'Karachi')
      formDataToSend.append('province', formData.province || 'Sindh')
      formDataToSend.append('admission_date', formData.admission_date || new Date().toISOString().split('T')[0])
      formDataToSend.append('nationality', formData.nationality || 'Pakistani')
      formDataToSend.append('religion', formData.religion || 'Islam')
      formDataToSend.append('marital_status', formData.marital_status || 'S')
      formDataToSend.append('is_active', 'true')
      
      // Add optional fields
      if (formData.cnic_number) formDataToSend.append('cnic', formData.cnic_number)
      if (formData.blood_group) formDataToSend.append('blood_group', formData.blood_group)
      if (formData.medical_conditions) formDataToSend.append('medical_conditions', formData.medical_conditions)
      if (formData.allergies) formDataToSend.append('allergies', formData.allergies)
      if (formData.emergency_contact) formDataToSend.append('emergency_medical_contact', formData.emergency_contact)
      if (formData.class_level) formDataToSend.append('class_level', formData.class_level)
      if (formData.section) formDataToSend.append('section', formData.section)
      if (formData.guardian_name) formDataToSend.append('guardian_name', formData.guardian_name)
      if (formData.guardian_phone) formDataToSend.append('guardian_phone', formData.guardian_phone)
      if (formData.guardian_email) formDataToSend.append('guardian_email', formData.guardian_email)
      if (formData.guardian_cnic) formDataToSend.append('guardian_cnic', formData.guardian_cnic)
      if (formData.guardian_relationship) formDataToSend.append('guardian_relationship', formData.guardian_relationship)
      if (formData.guardian_occupation) formDataToSend.append('guardian_occupation', formData.guardian_occupation)
      if (formData.academic_year) formDataToSend.append('academic_year', formData.academic_year)
      if (formData.term) formDataToSend.append('term', formData.term)
      if (formData.grade) formDataToSend.append('grade', formData.grade)
      if (formData.previous_school) formDataToSend.append('previous_school', formData.previous_school)
      if (formData.previous_education) formDataToSend.append('previous_education', formData.previous_education)
      if (formData.matric_marks) formDataToSend.append('matric_marks', formData.matric_marks)
      if (formData.intermediate_marks) formDataToSend.append('intermediate_marks', formData.intermediate_marks)
      if (formData.admission_number) formDataToSend.append('admission_number', formData.admission_number)
      if (formData.height) formDataToSend.append('height', formData.height)
      if (formData.weight) formDataToSend.append('weight', formData.weight)
      
      // Add photo if selected
      if (formData.photo) {
        formDataToSend.append('photo', formData.photo)
      }
      
      // Add documents if selected
      if (formData.birth_certificate) {
        formDataToSend.append('birth_certificate', formData.birth_certificate)
      }
      if (formData.academic_transcripts) {
        formDataToSend.append('academic_transcripts', formData.academic_transcripts)
      }
      if (formData.medical_records) {
        formDataToSend.append('medical_records', formData.medical_records)
      }
      if (formData.financial_documents) {
        formDataToSend.append('financial_documents', formData.financial_documents)
      }

      const response = await studentsService.createStudentWithFormData(formDataToSend)

      if (response.status === 'success') {
        toast({
          title: "Success",
          description: `Student created successfully! ID: ${response.data?.student_id || 'Generated'}`,
        })
        router.push('/students')
      } else {
        // Handle field-specific validation errors
        if (response.errors && typeof response.errors === 'object') {
          const errorMessages = Object.entries(response.errors).map(([field, errors]) => {
            const errorArray = Array.isArray(errors) ? errors : [errors]
            const fieldLabels: Record<string, string> = {
              first_name: 'First Name',
              last_name: 'Last Name',
              cnic: 'CNIC',
              guardian_cnic: 'Guardian CNIC',
              guardian_phone: 'Guardian Phone',
              guardian_email: 'Guardian Email',
              date_of_birth: 'Date of Birth',
              phone_number: 'Phone Number',
              email: 'Email'
            }
            return `${fieldLabels[field] || field}: ${errorArray[0]}`
          })
          
          const errorCount = Object.keys(response.errors).length
          toast({
            title: "Validation Error",
            description: errorCount === 1 
              ? errorMessages[0]
              : `${errorCount} errors found:\n${errorMessages.slice(0, 3).join('\n')}${errorCount > 3 ? `\n...and ${errorCount - 3} more` : ''}`,
            variant: "destructive",
          })
        } else {
          throw new Error(response.message || 'Failed to create student')
        }
      }
    } catch (error) {
      console.error('Error creating student:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create student",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveDraft = async () => {
    // For now, just show a message - could implement draft saving later
    toast({
      title: "Draft Saved",
      description: "Student draft saved locally",
    })
  }

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/students">
            <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Students
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add New Student</h1>
            <p className="text-gray-600 mt-1">Create a comprehensive student profile with all required information</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
            onClick={handleSaveDraft}
            disabled={isLoading}
          >
            Save as Draft
          </Button>
        </div>
      </div>

      <StudentForm 
        isEditing={false}
        onSubmit={handleSaveStudent}
        isLoading={isLoading}
      />
    </div>
  )
}