"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { DollarSign, Plus, Download, AlertCircle, CheckCircle, Users, ArrowUpRight, ArrowDownRight } from "lucide-react"

export default function FeesPage() {
  const [activeTab, setActiveTab] = useState("overview")

  const stats = [
    {
      title: "Total Revenue",
      value: "$245,680",
      change: "+12%",
      trend: "up",
      icon: DollarSign,
      color: "bg-green-500",
    },
    {
      title: "Pending Payments",
      value: "$45,230",
      change: "-8%",
      trend: "down",
      icon: AlertCircle,
      color: "bg-orange-500",
    },
    {
      title: "Collected Today",
      value: "$12,450",
      change: "+15%",
      trend: "up",
      icon: CheckCircle,
      color: "bg-blue-500",
    },
    {
      title: "Active Scholarships",
      value: "78",
      change: "+5%",
      trend: "up",
      icon: Users,
      color: "bg-purple-500",
    },
  ]

  const feeStructure = [
    { category: "Tuition Fee", amount: 15000, type: "Monthly", description: "Basic tuition charges" },
    { category: "Lab Fee", amount: 2000, type: "Semester", description: "Laboratory and practical charges" },
    { category: "Transport Fee", amount: 3000, type: "Monthly", description: "School transport service" },
    { category: "Library Fee", amount: 500, type: "Annual", description: "Library access and resources" },
    { category: "Sports Fee", amount: 1000, type: "Annual", description: "Sports activities and equipment" },
  ]

  const pendingPayments = [
    {
      studentId: "STU-2024-001",
      name: "Ahmed Hassan",
      class: "Grade 10-A",
      amount: 18000,
      dueDate: "2024-01-25",
      type: "Monthly Fee",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      studentId: "STU-2024-002",
      name: "Fatima Ali",
      class: "Grade 9-B",
      amount: 15000,
      dueDate: "2024-01-20",
      type: "Tuition Fee",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      studentId: "STU-2024-003",
      name: "Omar Khan",
      class: "Grade 11-A",
      amount: 3000,
      dueDate: "2024-01-22",
      type: "Transport Fee",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const scholarships = [
    { name: "Merit Scholarship", type: "Merit-based", discount: "50%", criteria: "Top 10% academic performance" },
    { name: "Need-based Aid", type: "Financial Aid", discount: "30%", criteria: "Family income below threshold" },
    { name: "Sports Scholarship", type: "Activity-based", discount: "25%", criteria: "Outstanding sports performance" },
  ]

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Fee Management</h1>
          <p className="text-gray-600 mt-1">Manage fee structure, payments, and financial operations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Reports
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <Plus className="h-4 w-4 mr-2" />
            Add Fee Category
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    {stat.trend === "up" && <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />}
                    {stat.trend === "down" && <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />}
                    <span className={`text-sm font-medium ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${stat.color}`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <TabsList className="grid w-full grid-cols-4 bg-indigo-50">
            <TabsTrigger value="overview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Overview
            </TabsTrigger>
            <TabsTrigger value="structure" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Fee Structure
            </TabsTrigger>
            <TabsTrigger value="payments" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
              Payments
            </TabsTrigger>
            <TabsTrigger
              value="scholarships"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              Scholarships
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Fee Collection Progress */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                <CardTitle className="text-lg text-green-900">Monthly Collection</CardTitle>
                <CardDescription>Current month progress</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600">Collected</span>
                    <span className="text-lg font-bold text-green-600">$200,450</span>
                  </div>
                  <Progress value={82} className="h-3" />
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>82% of target</span>
                    <span>Goal: $245,680</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Payments */}
            <Card className="lg:col-span-2 border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-lg text-indigo-900">Recent Payments</CardTitle>
                <CardDescription>Latest fee payments received</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-gray-100">
                  {[
                    { student: "Ahmed Hassan", amount: "$15,000", type: "Tuition Fee", time: "2 hours ago" },
                    { student: "Fatima Ali", amount: "$3,000", type: "Transport Fee", time: "4 hours ago" },
                    { student: "Omar Khan", amount: "$2,000", type: "Lab Fee", time: "6 hours ago" },
                  ].map((payment, index) => (
                    <div key={index} className="p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-green-100 rounded-full">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{payment.student}</p>
                            <p className="text-sm text-gray-500">{payment.type}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-green-600">{payment.amount}</p>
                          <p className="text-sm text-gray-500">{payment.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Overdue Payments */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 border-b">
              <CardTitle className="text-lg text-red-900">Overdue Payments</CardTitle>
              <CardDescription>Students with overdue fee payments requiring attention</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-100">
                {[
                  { student: "Sarah Ahmed", amount: "$18,000", days: "5 days overdue", type: "Monthly Fee" },
                  { student: "Ali Hassan", amount: "$15,000", days: "3 days overdue", type: "Tuition Fee" },
                  { student: "Zara Khan", amount: "$3,000", days: "2 days overdue", type: "Transport Fee" },
                ].map((payment, index) => (
                  <div key={index} className="p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-red-100 rounded-full">
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{payment.student}</p>
                          <p className="text-sm text-gray-500">{payment.type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-red-600">{payment.amount}</p>
                        <p className="text-sm text-red-500">{payment.days}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="structure" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Fee Structure Configuration</CardTitle>
              <CardDescription>Manage fee categories and amounts for different classes</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Category</TableHead>
                    <TableHead className="font-semibold text-gray-700">Amount</TableHead>
                    <TableHead className="font-semibold text-gray-700">Type</TableHead>
                    <TableHead className="font-semibold text-gray-700">Description</TableHead>
                    <TableHead className="font-semibold text-gray-700">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {feeStructure.map((fee, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium">{fee.category}</TableCell>
                      <TableCell className="font-semibold text-indigo-600">${fee.amount.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="border-indigo-200 text-indigo-700">
                          {fee.type}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-gray-600">{fee.description}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:bg-red-50">
                            Delete
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Payment Management</CardTitle>
              <CardDescription>Track and manage student fee payments</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <Input
                  placeholder="Search by student name or ID..."
                  className="flex-1 border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                />
                <Select>
                  <SelectTrigger className="w-48 border-gray-300">
                    <SelectValue placeholder="Payment Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Payments</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Student</TableHead>
                    <TableHead className="font-semibold text-gray-700">Student ID</TableHead>
                    <TableHead className="font-semibold text-gray-700">Class</TableHead>
                    <TableHead className="font-semibold text-gray-700">Amount</TableHead>
                    <TableHead className="font-semibold text-gray-700">Due Date</TableHead>
                    <TableHead className="font-semibold text-gray-700">Type</TableHead>
                    <TableHead className="font-semibold text-gray-700">Status</TableHead>
                    <TableHead className="font-semibold text-gray-700">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingPayments.map((payment, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <img
                            src={payment.avatar || "/placeholder.svg"}
                            alt={payment.name}
                            className="h-8 w-8 rounded-full object-cover"
                          />
                          <span className="font-medium">{payment.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{payment.studentId}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="border-indigo-200 text-indigo-700">
                          {payment.class}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-semibold text-red-600">${payment.amount.toLocaleString()}</TableCell>
                      <TableCell>{payment.dueDate}</TableCell>
                      <TableCell>{payment.type}</TableCell>
                      <TableCell>
                        <Badge variant="destructive" className="bg-red-100 text-red-800">
                          Pending
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" className="text-green-600 hover:bg-green-50">
                            Record Payment
                          </Button>
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            Send Reminder
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scholarships" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
              <CardTitle className="text-xl text-indigo-900">Scholarship Management</CardTitle>
              <CardDescription>Manage scholarships and financial aid programs</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Scholarship Name</TableHead>
                    <TableHead className="font-semibold text-gray-700">Type</TableHead>
                    <TableHead className="font-semibold text-gray-700">Discount</TableHead>
                    <TableHead className="font-semibold text-gray-700">Criteria</TableHead>
                    <TableHead className="font-semibold text-gray-700">Recipients</TableHead>
                    <TableHead className="font-semibold text-gray-700">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {scholarships.map((scholarship, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium">{scholarship.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="border-purple-200 text-purple-700">
                          {scholarship.type}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-semibold text-green-600">{scholarship.discount}</TableCell>
                      <TableCell className="text-gray-600">{scholarship.criteria}</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-indigo-100 text-indigo-800">
                          {Math.floor(Math.random() * 50) + 10} students
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                            View Recipients
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
