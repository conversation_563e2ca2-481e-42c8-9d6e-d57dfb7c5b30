[Unit]
Description=GIHD School Management System Backend
After=network.target

[Service]
Type=simple
User=ali
Group=ali
WorkingDirectory=/home/<USER>/development/GIHD_SCHOOL/backend
Environment=PATH=/home/<USER>/development/GIHD_SCHOOL/backend/venv/bin
Environment=DJANGO_SETTINGS_MODULE=gihd_sms.settings
Environment=PORT=5001
Environment=DEBUG=False
ExecStart=/home/<USER>/development/GIHD_SCHOOL/backend/venv/bin/python manage.py runserver 0.0.0.0:5001
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=3

[Install]
WantedBy=multi-user.target