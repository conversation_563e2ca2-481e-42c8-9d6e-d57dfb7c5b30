# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

GIHD School Management System is a full-stack web application built with:

- **Backend**: Django 5.2.4 REST API with JWT authentication
- **Frontend**: Next.js 15.2.4 with TypeScript and Tailwind CSS
- **Database**: SQLite (development)
- **Testing**: Playwright for end-to-end frontend testing

### Project Structure

```
├── backend/           # Django REST API
│   ├── gihd_sms/     # Main Django project
│   ├── students/     # Student management app
│   ├── academics/    # Academic records app
│   ├── fees/         # Fee management app
│   ├── documents/    # Document handling app
│   ├── reports/      # Reporting system app
│   ├── authentication/ # Custom auth app
│   └── common/       # Shared utilities and models
├── frontend/         # Next.js frontend
│   ├── app/          # Next.js App Router pages
│   ├── components/   # React components
│   ├── services/     # API service layer
│   ├── contexts/     # React contexts
│   └── tests/        # Playwright tests
```

## Development Commands

### Backend (Django)
```bash
cd backend
python manage.py runserver 5001    # Start development server
python manage.py migrate           # Run database migrations
python manage.py createsuperuser   # Create admin user
python manage.py collectstatic     # Collect static files
```

### Frontend (Next.js)
```bash
cd frontend
npm run dev          # Start development server on port 3000
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm test             # Run Playwright tests
npm run test:ui      # Run tests with UI
npm run test:debug   # Debug tests
```

### Testing Commands
```bash
# Frontend testing (Playwright)
npm run test:auth           # All authentication tests
npm run test:auth:login     # Login form and flow tests
npm run test:auth:session   # Session management tests
npm run test:report         # Show test report
npm run test:install        # Install Playwright browsers
```

## Key Backend Architecture

### Django Apps Structure
- **authentication**: Custom JWT-based auth with role-based permissions
- **students**: Student records, guardians, and notes
- **academics**: Academic records and course management
- **fees**: Fee collection and financial tracking
- **documents**: File uploads and document management
- **reports**: Reporting and data export functionality
- **common**: Shared utilities, decorators, and base classes

### API Design
- Standardized response format with `status`, `http_code`, `message`, and `data` fields
- JWT authentication using `djangorestframework-simplejwt`
- CORS configured for frontend integration
- Custom pagination class in `common.pagination`
- Custom exception handler in `common.exceptions`

### Key Models
- `Student`: Main student record with Pakistani identity validation (CNIC/B-Form)
- `Guardian`: Multiple guardian support with relationship tracking
- `StudentNote`: Notes system with confidentiality levels

## Key Frontend Architecture

### Tech Stack
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Radix UI** components via shadcn/ui
- **React Hook Form** with Zod validation
- **Context API** for state management

### Service Layer
- `BaseApiService`: Centralized API communication with standardized response handling
- Individual service files for each domain (students, auth, fees, etc.)
- Token management with automatic refresh
- Error handling and response standardization

### Authentication Flow
- JWT-based authentication with access/refresh token pattern
- Protected routes using `AuthGuard` and `ProtectedRoute` components
- Role-based access control
- Session persistence across browser tabs/refreshes

## Development Notes

### Backend Configuration
- Development uses SQLite database
- CORS configured for localhost:3000 and localhost:5000
- Media files served during development
- JWT tokens: 1-day access, 10-day refresh with rotation

### Frontend Configuration
- API base URL defaults to `http://localhost:5001/api`
- ESLint and TypeScript build errors ignored (development convenience)
- Images unoptimized for development

### Testing Strategy
- Comprehensive Playwright test suite covering authentication flows
- Test fixtures for different user roles (admin, teacher, staff, parent)
- Mobile responsiveness and accessibility testing
- Error handling and edge case coverage

## System Services

Frontend development server runs on port 5000 via systemctl
Backend development server runs on port 5001 via systemctl

Service files:
- `gihd-frontend.service`
- `gihd-backend.service`
- `install-services.sh` for setup 

Only use mcp playwright to perform the test that is attached to you

Do not hallucinate at all (AI hallucination refers to when an artificial intelligence model (like ChatGPT or others) produces content that sounds plausible but is factually incorrect, nonsensical, or entirely fabricated.)