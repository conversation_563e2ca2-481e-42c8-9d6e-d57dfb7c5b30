"""
Custom pagination classes for standardized API responses
"""
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from .response import StandardResponse


class StandardPageNumberPagination(PageNumberPagination):
    """
    Custom pagination that returns standardized response format
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        """
        Return a paginated style response with standard format
        """
        return StandardResponse.success(
            data={
                'results': data,
                'count': self.page.paginator.count,
                'next': self.get_next_link(),
                'previous': self.get_previous_link(),
                'pagination': {
                    'page': self.page.number,
                    'page_size': self.page.paginator.per_page,
                    'total_pages': self.page.paginator.num_pages,
                    'has_next': self.page.has_next(),
                    'has_previous': self.page.has_previous(),
                    'next_page': self.page.next_page_number() if self.page.has_next() else None,
                    'previous_page': self.page.previous_page_number() if self.page.has_previous() else None,
                }
            },
            message="Data retrieved successfully"
        )


class LargeResultsSetPagination(PageNumberPagination):
    """
    Pagination for large datasets
    """
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200
    
    def get_paginated_response(self, data):
        return StandardResponse.success(
            data={
                'results': data,
                'pagination': {
                    'count': self.page.paginator.count,
                    'page': self.page.number,
                    'page_size': self.page.paginator.per_page,
                    'total_pages': self.page.paginator.num_pages,
                    'has_next': self.page.has_next(),
                    'has_previous': self.page.has_previous(),
                    'next_page': self.page.next_page_number() if self.page.has_next() else None,
                    'previous_page': self.page.previous_page_number() if self.page.has_previous() else None,
                    'links': {
                        'next': self.get_next_link(),
                        'previous': self.get_previous_link()
                    }
                }
            },
            message="Data retrieved successfully"
        )


class SmallResultsSetPagination(PageNumberPagination):
    """
    Pagination for small datasets
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 50
    
    def get_paginated_response(self, data):
        return StandardResponse.success(
            data={
                'results': data,
                'pagination': {
                    'count': self.page.paginator.count,
                    'page': self.page.number,
                    'page_size': self.page.paginator.per_page,
                    'total_pages': self.page.paginator.num_pages,
                    'has_next': self.page.has_next(),
                    'has_previous': self.page.has_previous(),
                    'next_page': self.page.next_page_number() if self.page.has_next() else None,
                    'previous_page': self.page.previous_page_number() if self.page.has_previous() else None,
                    'links': {
                        'next': self.get_next_link(),
                        'previous': self.get_previous_link()
                    }
                }
            },
            message="Data retrieved successfully"
        )