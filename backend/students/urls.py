from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'', views.StudentViewSet, basename='student')

urlpatterns = [
    # ViewSet URLs (includes CRUD operations)
    path('', include(router.urls)),

    # Legacy endpoint
    path('legacy/', views.student_list, name='student_list_legacy'),
]