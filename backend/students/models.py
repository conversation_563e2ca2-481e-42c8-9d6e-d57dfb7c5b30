from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from simple_history.models import HistoricalRecords
from PIL import Image
import os
from datetime import date
import uuid


class Student(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]
    
    BLOOD_GROUP_CHOICES = [
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    ]
    
    MARITAL_STATUS_CHOICES = [
        ('S', 'Single'),
        ('M', 'Married'),
        ('D', 'Divorced'),
        ('W', 'Widowed'),
    ]
    
    RELIGION_CHOICES = [
        ('Islam', 'Islam'),
        ('Christianity', 'Christianity'),
        ('Hinduism', 'Hinduism'),
        ('Buddhism', 'Buddhism'),
        ('Other', 'Other'),
    ]

    # Basic Information
    student_id = models.CharField(max_length=20, unique=True, db_index=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    father_name = models.CharField(max_length=100)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    
    # Pakistani Identity Documents
    cnic_validator = RegexValidator(
        regex=r'^\d{5}-\d{7}-\d{1}$',
        message="CNIC must be in format: 12345-1234567-1"
    )
    cnic = models.CharField(
        max_length=15, 
        validators=[cnic_validator], 
        null=True, 
        blank=True,
        help_text="Format: 12345-1234567-1"
    )
    
    # For students under 18 or without CNIC
    b_form_number = models.CharField(
        max_length=20, 
        null=True, 
        blank=True,
        help_text="B-Form number for minors"
    )
    
    # Contact Information
    phone_validator = RegexValidator(
        regex=r'^\+92\d{10}$|^0\d{10}$',
        message="Phone must be in format: +923001234567 or 03001234567"
    )
    phone_number = models.CharField(
        max_length=15, 
        validators=[phone_validator],
        help_text="Format: +923001234567 or 03001234567"
    )
    email = models.EmailField(null=True, blank=True)
    
    # Address (Pakistani format)
    address_line_1 = models.CharField(max_length=200)
    address_line_2 = models.CharField(max_length=200, null=True, blank=True)
    city = models.CharField(max_length=50)
    province = models.CharField(max_length=50, choices=[
        ('Punjab', 'Punjab'),
        ('Sindh', 'Sindh'),
        ('KPK', 'Khyber Pakhtunkhwa'),
        ('Balochistan', 'Balochistan'),
        ('Gilgit-Baltistan', 'Gilgit-Baltistan'),
        ('AJK', 'Azad Jammu & Kashmir'),
        ('Islamabad', 'Islamabad Capital Territory'),
    ])
    postal_code = models.CharField(max_length=10, null=True, blank=True)
    
    # Personal Details
    religion = models.CharField(max_length=20, choices=RELIGION_CHOICES, default='Islam')
    nationality = models.CharField(max_length=50, default='Pakistani')
    marital_status = models.CharField(max_length=1, choices=MARITAL_STATUS_CHOICES, default='S')
    blood_group = models.CharField(max_length=3, choices=BLOOD_GROUP_CHOICES, null=True, blank=True)
    
    # Photo
    photo = models.ImageField(upload_to='student_photos/', null=True, blank=True)
    
    # Medical Information
    medical_conditions = models.TextField(null=True, blank=True)
    allergies = models.TextField(null=True, blank=True)
    emergency_medical_contact = models.CharField(max_length=15, null=True, blank=True)
    
    # Academic Information
    previous_education = models.TextField(null=True, blank=True)
    matric_marks = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    intermediate_marks = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Academic Enrollment Details
    academic_year = models.CharField(max_length=20, null=True, blank=True)
    term = models.CharField(max_length=20, null=True, blank=True)
    grade = models.CharField(max_length=10, null=True, blank=True)
    section = models.CharField(max_length=10, null=True, blank=True)
    class_level = models.CharField(max_length=10, null=True, blank=True)
    
    # Physical Information
    height = models.CharField(max_length=10, null=True, blank=True, help_text="Height in cm")
    weight = models.CharField(max_length=10, null=True, blank=True, help_text="Weight in kg")
    emergency_contact = models.CharField(max_length=15, null=True, blank=True)
    
    # System Fields
    is_active = models.BooleanField(default=True)
    admission_date = models.DateField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_students')
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-admission_date', 'student_id']
        indexes = [
            models.Index(fields=['student_id']),
            models.Index(fields=['cnic']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['admission_date']),
        ]
    
    def __str__(self):
        return f"{self.student_id} - {self.first_name} {self.last_name}"
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def get_display_name(self):
        return f"{self.get_full_name()} ({self.student_id})"
    
    def age(self):
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
    
    def save(self, *args, **kwargs):
        if not self.student_id:
            self.student_id = self.generate_student_id()
        
        # Resize photo if uploaded
        super().save(*args, **kwargs)
        if self.photo:
            self.resize_photo()
    
    def generate_student_id(self):
        current_year = date.today().year
        last_student = Student.objects.filter(
            student_id__startswith=f"GIHD{current_year}"
        ).order_by('student_id').last()
        
        if last_student:
            last_number = int(last_student.student_id[-4:])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"GIHD{current_year}{new_number:04d}"
    
    def resize_photo(self):
        if self.photo:
            img = Image.open(self.photo.path)
            if img.height > 400 or img.width > 400:
                img.thumbnail((400, 400))
                img.save(self.photo.path)


class Guardian(models.Model):
    RELATIONSHIP_CHOICES = [
        ('Father', 'Father'),
        ('Mother', 'Mother'),
        ('Brother', 'Brother'),
        ('Sister', 'Sister'),
        ('Uncle', 'Uncle'),
        ('Aunt', 'Aunt'),
        ('Grandfather', 'Grandfather'),
        ('Grandmother', 'Grandmother'),
        ('Guardian', 'Legal Guardian'),
        ('Other', 'Other'),
    ]
    
    GUARDIAN_TYPE_CHOICES = [
        ('Primary', 'Primary Guardian'),
        ('Secondary', 'Secondary Guardian'),
        ('Emergency', 'Emergency Contact'),
        ('Pickup', 'Authorized Pickup Person'),
    ]
    
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='guardians')
    guardian_type = models.CharField(max_length=10, choices=GUARDIAN_TYPE_CHOICES)
    
    # Personal Information
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    relationship = models.CharField(max_length=20, choices=RELATIONSHIP_CHOICES)
    
    # Pakistani Identity
    cnic_validator = RegexValidator(
        regex=r'^\d{5}-\d{7}-\d{1}$',
        message="CNIC must be in format: 12345-1234567-1"
    )
    cnic = models.CharField(
        max_length=15, 
        validators=[cnic_validator],
        help_text="Format: 12345-1234567-1"
    )
    
    # Contact Information
    phone_validator = RegexValidator(
        regex=r'^\+92\d{10}$|^0\d{10}$',
        message="Phone must be in format: +923001234567 or 03001234567"
    )
    phone_number = models.CharField(max_length=15, validators=[phone_validator])
    alternate_phone = models.CharField(max_length=15, validators=[phone_validator], null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    
    # Address
    address_line_1 = models.CharField(max_length=200)
    address_line_2 = models.CharField(max_length=200, null=True, blank=True)
    city = models.CharField(max_length=50)
    province = models.CharField(max_length=50, choices=[
        ('Punjab', 'Punjab'),
        ('Sindh', 'Sindh'),
        ('KPK', 'Khyber Pakhtunkhwa'),
        ('Balochistan', 'Balochistan'),
        ('Gilgit-Baltistan', 'Gilgit-Baltistan'),
        ('AJK', 'Azad Jammu & Kashmir'),
        ('Islamabad', 'Islamabad Capital Territory'),
    ])
    
    # Professional Information
    occupation = models.CharField(max_length=100, null=True, blank=True)
    workplace = models.CharField(max_length=200, null=True, blank=True)
    monthly_income = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Communication Preferences
    preferred_contact_method = models.CharField(max_length=10, choices=[
        ('Phone', 'Phone Call'),
        ('SMS', 'SMS'),
        ('Email', 'Email'),
        ('WhatsApp', 'WhatsApp'),
    ], default='Phone')
    
    can_pickup_student = models.BooleanField(default=False)
    is_emergency_contact = models.BooleanField(default=False)
    
    # System Fields
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['guardian_type', 'first_name']
        unique_together = [['student', 'cnic']]
        indexes = [
            models.Index(fields=['student', 'guardian_type']),
            models.Index(fields=['cnic']),
            models.Index(fields=['phone_number']),
        ]
    
    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.relationship} of {self.student.get_full_name()}"
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"


class StudentNote(models.Model):
    NOTE_TYPES = [
        ('Academic', 'Academic'),
        ('Behavioral', 'Behavioral'),
        ('Medical', 'Medical'),
        ('Administrative', 'Administrative'),
        ('Other', 'Other'),
    ]
    
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='notes')
    note_type = models.CharField(max_length=20, choices=NOTE_TYPES)
    title = models.CharField(max_length=200)
    content = models.TextField()
    is_confidential = models.BooleanField(default=False)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.student.get_full_name()}"
