from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, action
from rest_framework.response import Response
from django.db.models import Q
from common.response import StandardResponse
from common.mixins import StandardResponseMixin, ValidationMixin
from .models import <PERSON>, Guardian, StudentNote
from .serializers import StudentSerializer, GuardianSerializer, StudentNoteSerializer


class StudentViewSet(StandardResponseMixin, ValidationMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing students with full CRUD operations
    """
    queryset = Student.objects.all()
    serializer_class = StudentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Student.objects.all()

        # Filter parameters
        search = self.request.query_params.get('search')
        class_level = self.request.query_params.get('class_level')
        section = self.request.query_params.get('section')
        is_active = self.request.query_params.get('is_active')

        if search:
            queryset = queryset.filter(
                Q(student_id__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(father_name__icontains=search) |
                Q(cnic__icontains=search) |
                Q(phone_number__icontains=search)
            )

        if class_level:
            queryset = queryset.filter(class_level=class_level)

        if section:
            queryset = queryset.filter(section=section)

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('-admission_date', 'student_id')

    @action(detail=False, methods=['post'])
    def bulk_import(self, request):
        """Bulk import students from uploaded data"""
        students_data = request.data.get('students', [])

        if not students_data:
            return StandardResponse.validation_error(
                message="No student data provided",
                errors={'students': ['This field is required']}
            )

        success_count = 0
        errors = []

        for i, student_data in enumerate(students_data):
            try:
                serializer = self.get_serializer(data=student_data)
                if serializer.is_valid():
                    serializer.save()
                    success_count += 1
                else:
                    errors.append({
                        'row': i + 1,
                        'errors': serializer.errors
                    })
            except Exception as e:
                errors.append({
                    'row': i + 1,
                    'errors': {'general': [str(e)]}
                })

        return StandardResponse.success(
            data={
                'success': success_count,
                'errors': errors
            },
            message=f"Bulk import completed. {success_count} students imported successfully."
        )


@api_view(['GET'])
def student_list(request):
    """Legacy endpoint - redirects to ViewSet"""
    return Response({'message': 'Students API - Use /api/students/ with ViewSet endpoints'})
