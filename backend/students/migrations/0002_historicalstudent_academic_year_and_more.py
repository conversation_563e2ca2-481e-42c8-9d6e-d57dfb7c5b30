# Generated by Django 5.2.4 on 2025-07-28 17:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalstudent',
            name='academic_year',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='admission_number',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='class_level',
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='emergency_contact',
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='grade',
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='height',
            field=models.CharField(blank=True, help_text='Height in cm', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='section',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='term',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='historicalstudent',
            name='weight',
            field=models.CharField(blank=True, help_text='Weight in kg', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='academic_year',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='admission_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='class_level',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_contact',
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='grade',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='height',
            field=models.CharField(blank=True, help_text='Height in cm', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='section',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='term',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='weight',
            field=models.CharField(blank=True, help_text='Weight in kg', max_length=10, null=True),
        ),
    ]
