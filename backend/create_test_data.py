#!/usr/bin/env python3
"""
Script to create test data for GIHD School Management System
Run: python create_test_data.py
"""
import os
import sys
import django
from datetime import date, datetime, timedelta
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gihd_sms.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student, Guardian
from academics.models import AcademicYear, Department, Program, Session, Course, SessionCourse, Campus, Classroom
from fees.models import FeeCategory, FeeStructure, FeeStructureItem, Scholarship, StudentEnrollment, StudentPayment
from documents.models import StudentDocument, DocumentCategory, DocumentType

def create_users():
    """Create test users with different roles"""
    users_data = [
        {'username': 'admin', 'email': '<EMAIL>', 'first_name': 'Admin', 'last_name': 'User', 'is_staff': True, 'is_superuser': True},
        {'username': 'teacher1', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'is_staff': True},
        {'username': 'teacher2', 'email': '<EMAIL>', 'first_name': 'Ali', 'last_name': 'Hassan', 'is_staff': True},
        {'username': 'staff1', 'email': '<EMAIL>', 'first_name': 'Fatima', 'last_name': 'Khan', 'is_staff': True},
    ]
    
    created_users = {}
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults=user_data
        )
        if created:
            user.set_password('password123')
            user.save()
            print(f"Created user: {user.username}")
        created_users[user_data['username']] = user
    
    return created_users

def create_academic_years():
    """Create academic years"""
    years = [
        {'name': '2023-2024', 'start_date': date(2023, 4, 1), 'end_date': date(2024, 3, 31), 'is_active': False},
        {'name': '2024-2025', 'start_date': date(2024, 4, 1), 'end_date': date(2025, 3, 31), 'is_active': True},
    ]
    
    created_years = []
    for year_data in years:
        year, created = AcademicYear.objects.get_or_create(
            name=year_data['name'],
            defaults=year_data
        )
        if created:
            print(f"Created academic year: {year.name}")
        created_years.append(year)
    
    return created_years

def create_departments():
    """Create departments"""
    departments_data = [
        {'name': 'Computer Science', 'code': 'CS'},
        {'name': 'Business Administration', 'code': 'BA'},
        {'name': 'English Language', 'code': 'ENG'},
        {'name': 'Mathematics', 'code': 'MATH'},
    ]
    
    created_departments = []
    for dept_data in departments_data:
        dept, created = Department.objects.get_or_create(
            code=dept_data['code'],
            defaults=dept_data
        )
        if created:
            print(f"Created department: {dept.name}")
        created_departments.append(dept)
    
    return created_departments

def create_programs(departments):
    """Create programs"""
    programs_data = [
        {'name': 'Web Development Bootcamp', 'code': 'WDB2024', 'program_type': 'Bootcamp', 'department': 0, 'duration_value': 6, 'duration_type': 'Months'},
        {'name': 'Digital Marketing Course', 'code': 'DMC2024', 'program_type': 'Short Course', 'department': 1, 'duration_value': 3, 'duration_type': 'Months'},
        {'name': 'English Proficiency Program', 'code': 'EPP2024', 'program_type': 'Certificate', 'department': 2, 'duration_value': 4, 'duration_type': 'Months'},
        {'name': 'Data Science Workshop', 'code': 'DSW2024', 'program_type': 'Workshop', 'department': 3, 'duration_value': 2, 'duration_type': 'Weeks'},
    ]
    
    created_programs = []
    for prog_data in programs_data:
        prog, created = Program.objects.get_or_create(
            code=prog_data['code'],
            defaults={
                'name': prog_data['name'],
                'program_type': prog_data['program_type'],
                'department': departments[prog_data['department']],
                'duration_value': prog_data['duration_value'],
                'duration_type': prog_data['duration_type'],
                'description': f"Comprehensive {prog_data['name']} program",
                'max_students': 30,
                'min_students': 5,
            }
        )
        if created:
            print(f"Created program: {prog.name}")
        created_programs.append(prog)
    
    return created_programs

def create_sessions(programs, academic_years, users):
    """Create sessions"""
    created_sessions = []
    active_year = academic_years[1]  # 2024-2025
    
    for i, program in enumerate(programs):
        session, created = Session.objects.get_or_create(
            name=f"{program.name} - Spring 2024",
            program=program,
            academic_year=active_year,
            defaults={
                'session_type': 'Regular',
                'registration_start_date': date(2024, 3, 1),
                'registration_end_date': date(2024, 3, 31),
                'start_date': date(2024, 4, 1),
                'end_date': date(2024, 4, 1) + timedelta(days=program.get_duration_in_days()),
                'max_students': program.max_students,
                'min_students': program.min_students,
                'status': 'Active',
                'instructor': users['teacher1'] if i % 2 == 0 else users['teacher2'],
                'venue': f'Room {i+1}01',
                'created_by': users['admin'],
            }
        )
        if created:
            print(f"Created session: {session.name}")
        created_sessions.append(session)
    
    return created_sessions

def create_students():
    """Create test students"""
    students_data = [
        {
            'first_name': 'Ahmed', 'last_name': 'Ali', 'father_name': 'Muhammad Ali',
            'date_of_birth': date(1995, 5, 15), 'gender': 'M',
            'phone_number': '03001234567', 'email': '<EMAIL>',
            'address_line_1': 'House 123, Block A', 'city': 'Karachi', 'province': 'Sindh'
        },
        {
            'first_name': 'Fatima', 'last_name': 'Khan', 'father_name': 'Hassan Khan',
            'date_of_birth': date(1994, 8, 22), 'gender': 'F',
            'phone_number': '03001234568', 'email': '<EMAIL>',
            'address_line_1': 'House 456, Block B', 'city': 'Lahore', 'province': 'Punjab'
        },
        {
            'first_name': 'Hassan', 'last_name': 'Sheikh', 'father_name': 'Omar Sheikh',
            'date_of_birth': date(1996, 12, 10), 'gender': 'M',
            'phone_number': '03001234569', 'email': '<EMAIL>',
            'address_line_1': 'House 789, Block C', 'city': 'Islamabad', 'province': 'Islamabad'
        },
        {
            'first_name': 'Aisha', 'last_name': 'Siddiqui', 'father_name': 'Abdullah Siddiqui',
            'date_of_birth': date(1993, 3, 5), 'gender': 'F',
            'phone_number': '03001234570', 'email': '<EMAIL>',
            'address_line_1': 'House 012, Block D', 'city': 'Karachi', 'province': 'Sindh'
        },
        {
            'first_name': 'Omar', 'last_name': 'Malik', 'father_name': 'Rashid Malik',
            'date_of_birth': date(1997, 7, 18), 'gender': 'M',
            'phone_number': '03001234571', 'email': '<EMAIL>',
            'address_line_1': 'House 345, Block E', 'city': 'Faisalabad', 'province': 'Punjab'
        },
    ]
    
    created_students = []
    for student_data in students_data:
        student, created = Student.objects.get_or_create(
            phone_number=student_data['phone_number'],
            defaults=student_data
        )
        if created:
            print(f"Created student: {student.first_name} {student.last_name}")
        created_students.append(student)
    
    return created_students

def create_guardians(students):
    """Create guardians for students"""
    guardians_data = [
        {'student_idx': 0, 'first_name': 'Muhammad', 'last_name': 'Ali', 'relationship': 'Father', 'cnic': '42401-1234567-1'},
        {'student_idx': 1, 'first_name': 'Khadija', 'last_name': 'Khan', 'relationship': 'Mother', 'cnic': '35202-2345678-2'},
        {'student_idx': 2, 'first_name': 'Omar', 'last_name': 'Sheikh', 'relationship': 'Father', 'cnic': '61101-3456789-3'},
        {'student_idx': 3, 'first_name': 'Maryam', 'last_name': 'Siddiqui', 'relationship': 'Mother', 'cnic': '42301-4567890-4'},
        {'student_idx': 4, 'first_name': 'Rashid', 'last_name': 'Malik', 'relationship': 'Father', 'cnic': '33101-5678901-5'},
    ]
    
    created_guardians = []
    for guardian_data in guardians_data:
        student = students[guardian_data['student_idx']]
        guardian, created = Guardian.objects.get_or_create(
            student=student,
            cnic=guardian_data['cnic'],
            defaults={
                'guardian_type': 'Primary',
                'first_name': guardian_data['first_name'],
                'last_name': guardian_data['last_name'],
                'relationship': guardian_data['relationship'],
                'phone_number': f"0300{1234567 + guardian_data['student_idx']}",
                'email': f"{guardian_data['first_name'].lower()}.{guardian_data['last_name'].lower()}@gmail.com",
                'address_line_1': student.address_line_1,
                'city': student.city,
                'province': student.province,
                'can_pickup_student': True,
                'is_emergency_contact': True,
            }
        )
        if created:
            print(f"Created guardian: {guardian.first_name} {guardian.last_name}")
        created_guardians.append(guardian)
    
    return created_guardians

def create_fee_categories():
    """Create fee categories"""
    categories_data = [
        {'name': 'Course Fee', 'category_type': 'Tuition'},
        {'name': 'Registration Fee', 'category_type': 'Registration'},
        {'name': 'Lab Fee', 'category_type': 'Lab'},
        {'name': 'Library Fee', 'category_type': 'Library'},
        {'name': 'Technology Fee', 'category_type': 'Technology'},
    ]
    
    created_categories = []
    for cat_data in categories_data:
        category, created = FeeCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        if created:
            print(f"Created fee category: {category.name}")
        created_categories.append(category)
    
    return created_categories

def create_fee_structures(programs, academic_years, fee_categories, users):
    """Create fee structures"""
    active_year = academic_years[1]  # 2024-2025
    created_structures = []
    
    for program in programs:
        # Base fee varies by program type
        base_fees = {
            'Bootcamp': 150000,
            'Short Course': 75000,
            'Certificate': 100000,
            'Workshop': 25000,
        }
        base_fee = base_fees.get(program.program_type, 50000)
        
        fee_structure, created = FeeStructure.objects.get_or_create(
            name=f"{program.name} Fee Structure",
            program=program,
            academic_year=active_year,
            defaults={
                'effective_from': active_year.start_date,
                'effective_to': active_year.end_date,
                'total_fee': base_fee,
                'created_by': users['admin'],
            }
        )
        
        if created:
            print(f"Created fee structure: {fee_structure.name}")
            
            # Create fee structure items
            fee_breakdown = {
                0: base_fee * 0.8,    # Course Fee (80%)
                1: base_fee * 0.05,   # Registration Fee (5%)
                2: base_fee * 0.1,    # Lab Fee (10%)
                3: base_fee * 0.03,   # Library Fee (3%)
                4: base_fee * 0.02,   # Technology Fee (2%)
            }
            
            for i, (cat_idx, amount) in enumerate(fee_breakdown.items()):
                FeeStructureItem.objects.create(
                    fee_structure=fee_structure,
                    fee_category=fee_categories[cat_idx],
                    amount=amount,
                    is_mandatory=True,
                    due_after_enrollment=7 if cat_idx == 1 else 30,  # Registration fee due in 7 days
                )
        
        created_structures.append(fee_structure)
    
    return created_structures

def create_enrollments(students, sessions, fee_structures, users):
    """Create student enrollments"""
    created_enrollments = []
    
    for i, student in enumerate(students):
        session = sessions[i % len(sessions)]  # Distribute students across sessions
        fee_structure = None
        
        # Find matching fee structure
        for fs in fee_structures:
            if fs.program == session.program:
                fee_structure = fs
                break
        
        if fee_structure:
            enrollment, created = StudentEnrollment.objects.get_or_create(
                student=student,
                session=session,
                defaults={
                    'fee_structure': fee_structure,
                    'status': 'Enrolled',
                    'total_fee': fee_structure.total_fee,
                    'net_fee': fee_structure.total_fee,
                    'fee_due_date': date.today() + timedelta(days=30),
                    'created_by': users['admin'],
                }
            )
            if created:
                print(f"Created enrollment: {student.first_name} in {session.name}")
            created_enrollments.append(enrollment)
    
    return created_enrollments

def create_payments(enrollments, users):
    """Create payment records"""
    created_payments = []
    
    for enrollment in enrollments[:3]:  # Create payments for first 3 enrollments
        # Create partial payment
        amount = enrollment.net_fee / 2  # Pay half
        
        payment, created = StudentPayment.objects.get_or_create(
            enrollment=enrollment,
            amount=amount,
            defaults={
                'payment_method': 'Bank Transfer',
                'payment_date': date.today() - timedelta(days=15),
                'reference_number': f'TXN{enrollment.id}{date.today().strftime("%Y%m%d")}',
                'status': 'Confirmed',
                'confirmation_date': datetime.now(),
                'confirmed_by': users['staff1'],
                'created_by': users['staff1'],
            }
        )
        if created:
            print(f"Created payment: PKR {amount} for {enrollment.student.first_name}")
        created_payments.append(payment)
    
    return created_payments

def create_document_categories():
    """Create document categories"""
    categories_data = [
        {'name': 'CNIC Copy', 'category_type': 'Identity', 'is_required': True},
        {'name': 'Passport Photo', 'category_type': 'Photo', 'is_required': True},
        {'name': 'Education Certificate', 'category_type': 'Academic', 'is_required': True},
    ]

    created_categories = []
    for cat_data in categories_data:
        category, created = DocumentCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        if created:
            print(f"Created document category: {category.name}")
        created_categories.append(category)

    return created_categories

def create_document_types(categories):
    """Create document types"""
    types_data = [
        {'name': 'CNIC Copy', 'category_idx': 0, 'is_required': True},
        {'name': 'Passport Photo', 'category_idx': 1, 'is_required': True},
        {'name': 'Matric Certificate', 'category_idx': 2, 'is_required': True},
    ]

    created_types = []
    for type_data in types_data:
        doc_type, created = DocumentType.objects.get_or_create(
            name=type_data['name'],
            category=categories[type_data['category_idx']],
            defaults={
                'is_required': type_data['is_required'],
                'description': f"{type_data['name']} for student verification"
            }
        )
        if created:
            print(f"Created document type: {doc_type.name}")
        created_types.append(doc_type)

    return created_types

def main():
    """Main function to create all test data"""
    print("Creating test data for GIHD School Management System...")
    print("=" * 60)
    
    try:
        # Create all test data in order
        users = create_users()
        academic_years = create_academic_years()
        departments = create_departments()
        programs = create_programs(departments)
        sessions = create_sessions(programs, academic_years, users)
        students = create_students()
        guardians = create_guardians(students)
        fee_categories = create_fee_categories()
        fee_structures = create_fee_structures(programs, academic_years, fee_categories, users)
        enrollments = create_enrollments(students, sessions, fee_structures, users)
        payments = create_payments(enrollments, users)
        document_categories = create_document_categories()
        document_types = create_document_types(document_categories)
        
        print("=" * 60)
        print("Test data creation completed successfully!")
        print(f"Summary:")
        print(f"  - {len(users)} Users")
        print(f"  - {len(academic_years)} Academic Years")
        print(f"  - {len(departments)} Departments")
        print(f"  - {len(programs)} Programs")
        print(f"  - {len(sessions)} Sessions")
        print(f"  - {len(students)} Students")
        print(f"  - {len(guardians)} Guardians")
        print(f"  - {len(fee_categories)} Fee Categories")
        print(f"  - {len(fee_structures)} Fee Structures")
        print(f"  - {len(enrollments)} Enrollments")
        print(f"  - {len(payments)} Payments")
        print(f"  - {len(document_categories)} Document Categories")
        print(f"  - {len(document_types)} Document Types")
        
        print("\nTest login credentials:")
        print("Admin: admin / password123")
        print("Teacher: teacher1 / password123")
        print("Staff: staff1 / password123")
        
    except Exception as e:
        print(f"Error creating test data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()